{"name": "customchess", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.4.1", "@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "chess.js": "^1.4.0", "clsx": "^2.1.1", "lucide-react": "^0.522.0", "next": "^13.5.6", "openai": "^5.6.0", "react": "^18.2.0", "react-chessboard": "^4.7.3", "react-dom": "^18.2.0", "supabase": "^2.26.9", "tailwind-merge": "^3.3.1", "utf-8-validate": "^6.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "^13.5.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}