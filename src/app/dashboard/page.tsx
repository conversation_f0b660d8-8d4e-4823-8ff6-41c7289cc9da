"use client";

import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Crown,
  Plus,
  Settings,
  LogOut,
  Palette,
  Users,
  Trophy,
} from "lucide-react";
import { Database } from "@/types/database";

type Board = Database["public"]["Tables"]["boards"]["Row"];

export default function Dashboard() {
  const { user, signOut, supabase } = useAuth();
  const router = useRouter();
  const [boards, setBoards] = useState<Board[]>([]);
  const [isFetchingBoards, setIsFetchingBoards] = useState(true);

  useEffect(() => {
    const fetchBoards = async () => {
      if (!user) return;
      setIsFetchingBoards(true);
      const { data, error } = await supabase
        .from("boards")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (data) {
        setBoards(data);
      }
      setIsFetchingBoards(false);
    };

    if (user) {
      fetchBoards();
    }
  }, [user]);

  const handleCreateGame = async (boardId: string) => {
    if (!user) return;

    // Create a new game record in Supabase
    const { data, error } = await supabase
      .from("games")
      .insert({
        board_id: boardId,
        player1_id: user.id,
        // player2_id will be set when someone joins
        status: "waiting",
        state: {
          fen: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
          turn: "white",
        }, // Default starting position
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating game:", error);
      // Handle error with a user-facing message
      return;
    }

    // Redirect to the new game room
    router.push(`/play/${data.id}`);
  };

  const handleSignOut = async () => {
    await signOut();
    router.push("/");
  };

  if (!user) {
    console.log("no user");
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Crown className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">CustomChess</h1>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-gray-600">Welcome, {user.email}</span>
            <button
              onClick={handleSignOut}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h2>
          <p className="text-gray-600">
            Create custom chess boards, manage your pieces, and play with
            friends.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <button className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow text-left">
            <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Plus className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Create New Board
            </h3>
            <p className="text-gray-600 text-sm">
              Design a custom chess board with your own pieces and themes.
            </p>
          </button>

          <button className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow text-left">
            <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Join Game
            </h3>
            <p className="text-gray-600 text-sm">
              Enter a game code to join a friend's custom chess game.
            </p>
          </button>

          <button className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-shadow text-left">
            <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Trophy className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Game History
            </h3>
            <p className="text-gray-600 text-sm">
              View your past games and analyze your performance.
            </p>
          </button>
        </div>

        {/* My Boards Section */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">
              My Chess Boards
            </h3>
            <button
              onClick={() => router.push("/board-editor")}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>New Board</span>
            </button>
          </div>

          {isFetchingBoards ? (
            <div className="text-center py-12">Loading boards...</div>
          ) : boards.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {boards.map((board) => (
                <div
                  key={board.id}
                  className="border rounded-lg p-4 flex flex-col justify-between"
                >
                  <div>
                    <h4 className="font-semibold text-lg">{board.name}</h4>
                    <p className="text-sm text-gray-500">
                      Created on{" "}
                      {new Date(board.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <button
                    onClick={() => handleCreateGame(board.id)}
                    className="mt-4 w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Create Game Room
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Palette className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                No boards yet
              </h4>
              <p className="text-gray-600 mb-6">
                Create your first custom chess board to get started.
              </p>
              <button
                onClick={() => router.push("/board-editor")}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Your First Board
              </button>
            </div>
          )}
        </div>

        {/* Recent Games Section */}
        <div className="bg-white rounded-xl shadow-sm border p-6 mt-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            Recent Games
          </h3>

          {/* Empty State */}
          <div className="text-center py-12">
            <Trophy className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No games played yet
            </h4>
            <p className="text-gray-600">
              Start playing games with your custom boards to see them here.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
