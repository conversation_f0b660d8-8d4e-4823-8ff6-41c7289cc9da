'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { ChessBoard } from '@/components/ChessBoard'
import { BoardCustomizer } from '@/components/BoardCustomizer'
import { ImageManager } from '@/components/ImageManager'
import { Save, ArrowLeft, Palette, Crown, CheckCircle, AlertCircle, Settings, Image, Loader2 } from 'lucide-react'
import { saveBoard } from '@/lib/board-utils'

export default function BoardEditorPage() {
  const { user, supabase } = useAuth()
  const router = useRouter()
  
  const [boardName, setBoardName] = useState('My Custom Board')
  const [fen, setFen] = useState('rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1')
  const [customPieces, setCustomPieces] = useState<Record<string, string>>({})
  const [boardTheme, setBoardTheme] = useState({
    lightSquares: '#f0d9b5',
    darkSquares: '#b58863',
    borderColor: '#8b4513'
  })

  const [activeTab, setActiveTab] = useState('pieces') // pieces or board
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<{type: 'success' | 'error' | null, message: string}>({ type: null, message: '' })

  const handleCustomPiecesChange = (newPieces: Record<string, string>) => {
    setCustomPieces(newPieces)
  }

  const handleSaveBoard = async () => {
    if (!user || !supabase) return

    setIsSaving(true)
    setSaveStatus({ type: null, message: '' })

    try {
      const result = await saveBoard(supabase, user.id, boardName, boardTheme, customPieces)
      
      if (result.success) {
        setSaveStatus({ type: 'success', message: 'Board saved successfully!' })
        setTimeout(() => router.push('/dashboard'), 1500)
      } else {
        throw new Error(result.error || 'Failed to save board.')
      }
    } catch (err: any) {
      setSaveStatus({ type: 'error', message: err.message })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
          <div className="flex items-center gap-4">
              <button onClick={() => router.push('/dashboard')} className="p-2 rounded-md hover:bg-gray-700">
                  <ArrowLeft size={20} />
              </button>
              <input
                  type="text"
                  value={boardName}
                  onChange={(e) => setBoardName(e.target.value)}
                  className="bg-transparent text-xl font-bold focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-md px-2 py-1"
              />
          </div>
          <button
              onClick={handleSaveBoard}
              disabled={isSaving}
              className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white font-semibold rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50"
          >
              {isSaving ? <Loader2 className="animate-spin" /> : <Save size={18} />}
              <span>{isSaving ? 'Saving...' : 'Save Board'}</span>
          </button>
      </header>

      {/* Main Content */}
      <div className="flex-grow flex flex-col md:flex-row bg-gray-800 text-white overflow-hidden">
        {/* Left Panel: Chessboard Preview */}
        <div className="w-full md:w-3/5 p-4 flex items-center justify-center bg-gray-900">
          <ChessBoard position={fen} boardTheme={boardTheme} pieceSet="classic" />
        </div>

        {/* Right Panel: Customization */}
        <div className="w-full md:w-2/5 p-6 bg-gray-800 overflow-y-auto">
          <div className="flex items-center border-b-2 border-gray-700 mb-4">
            <button onClick={() => setActiveTab('pieces')} className={`flex items-center gap-2 py-2 px-4 text-sm font-medium transition-colors ${activeTab === 'pieces' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}>
              <Image size={18} /> Custom Pieces
            </button>
            <button onClick={() => setActiveTab('board')} className={`flex items-center gap-2 py-2 px-4 text-sm font-medium transition-colors ${activeTab === 'board' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}>
              <Palette size={18} /> Board Theme
            </button>
          </div>

          {activeTab === 'pieces' ? (
            <ImageManager
              customPieces={customPieces}
              onCustomPiecesChange={handleCustomPiecesChange}
            />
          ) : (
            <BoardCustomizer
              boardTheme={boardTheme}
              onThemeChange={setBoardTheme}
            />
          )}
        </div>
      </div>
       {saveStatus.type && (
          <div className={`fixed bottom-5 right-5 p-4 rounded-lg text-white flex items-center gap-3 ${saveStatus.type === 'success' ? 'bg-green-600' : 'bg-red-600'}`}>
              {saveStatus.type === 'success' ? <CheckCircle /> : <AlertCircle />}
              <span>{saveStatus.message}</span>
          </div>
       )}
    </div>
  )
}
