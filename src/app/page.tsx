"use client";

import Link from "next/link";
import { ChevronR<PERSON>, Crown, Palette, Users } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Crown className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">CustomChess</h1>
          </div>
          <nav className="flex items-center space-x-6">
            {user ? (
              <Link
                href="/dashboard"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Dashboard
              </Link>
            ) : (
              <>
                <Link
                  href="/auth"
                  className="text-gray-600 hover:text-gray-900"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth"
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Get Started
                </Link>
              </>
            )}
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Create Your Own Chess Experience
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Design custom chess boards with personalized pieces, choose your
            themes, and play online with friends in a truly unique chess
            environment.
          </p>
          <Link
            href={user ? "/dashboard" : "/auth"}
            className="inline-flex items-center bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            {user ? "Go to Dashboard" : "Start Creating"}
            <ChevronRight className="ml-2 h-5 w-5" />
          </Link>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mt-20">
          <div className="bg-white rounded-xl p-8 shadow-sm border">
            <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Palette className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Custom Pieces
            </h3>
            <p className="text-gray-600">
              Upload your own piece designs or generate unique pieces with AI.
              Create themed sets that reflect your personality.
            </p>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-sm border">
            <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Crown className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Board Themes
            </h3>
            <p className="text-gray-600">
              Customize your board with different colors, patterns, and
              backgrounds. Make every game visually stunning.
            </p>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-sm border">
            <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              Online Play
            </h3>
            <p className="text-gray-600">
              Share your custom boards and play real-time games with friends.
              Multiple timer options and game modes available.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
