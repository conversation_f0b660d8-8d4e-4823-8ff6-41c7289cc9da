import { createClient } from "@/lib/supabase/server";
import { OpenAI } from "openai";
import { NextResponse } from "next/server";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: Request) {
  const supabase = createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { prompt } = await request.json();

  if (!prompt) {
    return NextResponse.json({ error: "Missing prompt" }, { status: 400 });
  }

  try {
    // First, moderate the text prompt
    const moderationResponse = await openai.moderations.create({
      input: prompt,
    });
    const moderationResult = moderationResponse.results[0];

    if (moderationResult.flagged) {
      return NextResponse.json(
        { error: "Prompt violates content policy" },
        { status: 400 }
      );
    }

    // Generate the image using GPT-Image-1
    const imageResponse = await openai.images.generate({
      model: "gpt-image-1",
      quality: "medium",
      prompt: `A single, clean, high-resolution, centered icon of a chess piece, in a ${prompt} style. The image must have a transparent background and be suitable for a virtual chess game. No text or extra elements.`,
      n: 1,
      size: "1024x1024",
      background: "transparent",
    });

    const base64Image = imageResponse.data?.[0]?.b64_json;
    if (!base64Image) {
      throw new Error("Image generation failed: No base64 data returned.");
    }

    // Convert base64 to blob for upload to Supabase
    const imageBuffer = Buffer.from(base64Image, "base64");
    const imageBlob = new Blob([imageBuffer], { type: "image/png" });
    const filePath = `${user.id}/ai-${Date.now()}.png`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from("piece_images")
      .upload(filePath, imageBlob, {
        cacheControl: "3600",
        upsert: true,
      });

    if (uploadError) {
      throw new Error(`Storage error: ${uploadError.message}`);
    }

    // Get public URL and save to database
    const { data: publicUrlData } = supabase.storage
      .from("piece_images")
      .getPublicUrl(uploadData.path);

    const finalUrl = publicUrlData.publicUrl;
    const imageName = `AI: ${prompt.substring(0, 40)}${
      prompt.length > 40 ? "..." : ""
    }`;

    const { data: dbData, error: dbError } = await supabase
      .from("custom_images")
      .insert({
        user_id: user.id,
        image_url: finalUrl,
        name: imageName,
      })
      .select()
      .single();

    if (dbError) {
      await supabase.storage.from("piece_images").remove([uploadData.path]);
      throw new Error(`Database error: ${dbError.message}`);
    }

    return NextResponse.json(dbData);
  } catch (error: any) {
    console.error("AI Generation Error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
