import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const { data, error } = await supabase
    .from('custom_images')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json(data)
}

export async function POST(request: Request) {
  const supabase = createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const formData = await request.formData()
  const file = formData.get('image') as File

  if (!file) {
    return NextResponse.json({ error: 'No file provided' }, { status: 400 })
  }

  // Upload to storage
  const filePath = `${user.id}/${Date.now()}-${file.name}`
  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('piece_images')
    .upload(filePath, file)

  if (uploadError) {
    return NextResponse.json({ error: `Storage error: ${uploadError.message}` }, { status: 500 })
  }

  // Get public URL
  const { data: publicUrlData } = supabase.storage
    .from('piece_images')
    .getPublicUrl(uploadData.path)

  // Insert into database
  const { data: dbData, error: dbError } = await supabase
    .from('custom_images')
    .insert({
      user_id: user.id,
      image_url: publicUrlData.publicUrl,
      name: file.name,
    })
    .select()
    .single()

  if (dbError) {
    // If DB insert fails, try to delete the orphaned storage object
    await supabase.storage.from('piece_images').remove([uploadData.path])
    return NextResponse.json({ error: `Database error: ${dbError.message}` }, { status: 500 })
  }

  return NextResponse.json(dbData)
} 