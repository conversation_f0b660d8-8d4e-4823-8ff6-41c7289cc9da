import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function DELETE(
  request: Request,
  { params }: { params: { imageId: string } }
) {
  const supabase = createClient()
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const imageId = params.imageId

  // First, get the image URL to delete from storage
  const { data: image, error: fetchError } = await supabase
    .from('custom_images')
    .select('image_url')
    .eq('id', imageId)
    .eq('user_id', user.id)
    .single()

  if (fetchError || !image) {
    return NextResponse.json(
      { error: 'Image not found or access denied' },
      { status: 404 }
    )
  }

  // Then, delete the database record
  const { error: dbError } = await supabase
    .from('custom_images')
    .delete()
    .eq('id', imageId)

  if (dbError) {
    return NextResponse.json(
      { error: `Database error: ${dbError.message}` },
      { status: 500 }
    )
  }

  // Finally, delete the file from storage
  try {
    const url = new URL(image.image_url)
    const path = url.pathname.split('/piece_images/')[1]
    if (path) {
      await supabase.storage.from('piece_images').remove([path])
    }
  } catch (e) {
    console.error('Failed to delete from storage, but DB record is gone:', e)
  }

  return NextResponse.json({ success: true })
} 