'use client'

import { useState, useEffect } from 'react'
import { notFound, useParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { ChessBoard } from '@/components/ChessBoard'
import { Database } from '@/types/database'
import { Chess } from 'chess.js'
// We will create these components later
// import { GameInfo } from '@/components/GameInfo'
// import { MoveHistory } from '@/components/MoveHistory'
// import { GameActions } from '@/components/GameActions'

type Game = Database['public']['Tables']['games']['Row']

export default function GameRoomPage() {
  const { gameId } = useParams()
  const { user } = useAuth()
  const [game, setGame] = useState<Game | null>(null)
  const [chess, setChess] = useState(new Chess())
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!gameId) return

    const fetchGame = async () => {
      setLoading(true)
      const { data, error } = await supabase
        .from('games')
        .select('*')
        .eq('id', gameId)
        .single()

      if (error || !data) {
        setError('Game not found or an error occurred.')
        notFound()
        return
      }
      
      setGame(data)
      setLoading(false)

      // If user is not player1 and there is no player2, join the game
      if (user && data.player1_id !== user.id && !data.player2_id) {
        const { error: updateError } = await supabase
          .from('games')
          .update({ player2_id: user.id, status: 'playing' })
          .eq('id', gameId)
        
        if (updateError) {
          setError('Failed to join the game.')
        }
      }

      // When the game state changes, update the chess.js instance
      if (data) {
        setChess(new Chess(data.state.fen))
      }
    }

    fetchGame()

    // Set up a real-time subscription
    const channel = supabase
      .channel(`game:${gameId}`)
      .on<Game>(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'games', filter: `id=eq.${gameId}` },
        (payload) => {
          setGame(payload.new as Game)
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [gameId, user])

  const handleMove = async (from: string, to: string) => {
    if (!game || !user) return

    const gameClone = new Chess(game.state.fen)
    const move = gameClone.move({ from, to, promotion: 'q' }) // Default promotion to queen

    if (move === null) {
      return // Illegal move
    }

    // Check if it's the user's turn
    const isPlayer1 = user.id === game.player1_id
    const isPlayer2 = user.id === game.player2_id
    const turn = gameClone.turn()
    
    if ((isPlayer1 && turn !== 'w') || (isPlayer2 && turn !== 'b')) {
      return // Not your turn
    }

    // Update the game state in Supabase
    const { error } = await supabase
      .from('games')
      .update({
        state: { fen: gameClone.fen(), turn: turn },
        pgn_history: gameClone.pgn(),
      })
      .eq('id', gameId)

    if (error) {
      console.error('Failed to update game state:', error)
      // The UI will eventually be corrected by the real-time subscription
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-xl font-semibold">Loading Game...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-xl font-semibold text-red-600">{error}</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Game Room</h1>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 bg-white rounded-lg shadow p-6">
            <ChessBoard
              position={chess.fen()}
              onPieceDrop={handleMove}
              boardOrientation={user?.id === game?.player1_id ? 'white' : 'black'}
              boardTheme={{ lightSquares: '#f0d9b5', darkSquares: '#b58863', borderColor: '#8b4513' }} // Placeholder
              pieceSet="classic" // Placeholder
            />
          </div>
          <div className="space-y-6">
            {/* GameInfo, MoveHistory, and GameActions will go here */}
            <div className="bg-white rounded-lg shadow p-4">
              <h2 className="text-lg font-semibold">Game Info</h2>
              <p>Status: {game?.status}</p>
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <h2 className="text-lg font-semibold">Move History</h2>
              {/* Move history will be rendered here */}
            </div>
            <div className="bg-white rounded-lg shadow p-4">
              <h2 className="text-lg font-semibold">Actions</h2>
              {/* Game actions (resign, draw) will go here */}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 