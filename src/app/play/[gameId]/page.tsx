"use client";

import { useState, useEffect } from "react";
import { notFound, useParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { ChessBoard } from "@/components/ChessBoard";
import { Database } from "@/types/database";
import { Chess } from "chess.js";
import { Share2, Copy, Check } from "lucide-react";
// We will create these components later
// import { GameInfo } from '@/components/GameInfo'
// import { MoveHistory } from '@/components/MoveHistory'
// import { GameActions } from '@/components/GameActions'

type Game = Database["public"]["Tables"]["games"]["Row"];
type Board = Database["public"]["Tables"]["boards"]["Row"];
type Piece = Database["public"]["Tables"]["pieces"]["Row"];

export default function GameRoomPage() {
  const { gameId } = useParams();
  const { user, supabase } = useAuth();
  const [game, setGame] = useState<Game | null>(null);
  const [board, setBoard] = useState<Board | null>(null);
  const [customPieces, setCustomPieces] = useState<Record<string, string>>({});
  const [chess, setChess] = useState(new Chess());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (!gameId) return;

    const fetchGame = async () => {
      setLoading(true);
      const { data: gameData, error: gameError } = await supabase
        .from("games")
        .select("*")
        .eq("id", gameId)
        .single();

      if (gameError || !gameData) {
        setError("Game not found or an error occurred.");
        notFound();
        return;
      }

      setGame(gameData);

      // Fetch board data and custom pieces
      const { data: boardData, error: boardError } = await supabase
        .from("boards")
        .select("*")
        .eq("id", gameData.board_id)
        .single();

      if (boardError || !boardData) {
        setError("Board not found.");
        return;
      }

      setBoard(boardData);

      // Fetch custom pieces for this board
      const { data: piecesData, error: piecesError } = await supabase
        .from("pieces")
        .select("*")
        .eq("board_id", gameData.board_id);

      if (!piecesError && piecesData) {
        // Convert pieces data to the format expected by ChessBoard component
        const piecesMap: Record<string, string> = {};
        piecesData.forEach((piece) => {
          // Convert database format to chess notation (e.g., "white king" -> "wK")
          const colorPrefix = piece.color === "white" ? "w" : "b";
          const typeMap: Record<string, string> = {
            king: "K",
            queen: "Q",
            rook: "R",
            knight: "N",
            bishop: "B",
            pawn: "P",
          };
          const pieceKey = `${colorPrefix}${typeMap[piece.type]}`;
          piecesMap[pieceKey] = piece.image_url;
        });
        setCustomPieces(piecesMap);
      }

      setLoading(false);

      // If user is not player1 and there is no player2, join the game
      if (user && gameData.player1_id !== user.id && !gameData.player2_id) {
        const { error: updateError } = await supabase
          .from("games")
          .update({ player2_id: user.id, status: "playing" })
          .eq("id", gameId);

        if (updateError) {
          setError("Failed to join the game.");
        }
      }

      // When the game state changes, update the chess.js instance
      if (gameData) {
        setChess(new Chess(gameData.state.fen));
      }
    };

    fetchGame();

    // Set up a real-time subscription
    const channel = supabase
      .channel(`game:${gameId}`)
      .on<Game>(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "games",
          filter: `id=eq.${gameId}`,
        },
        (payload) => {
          setGame(payload.new as Game);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [gameId, user]);

  const handleMove = async (from: string, to: string) => {
    if (!game || !user) return;

    const gameClone = new Chess(game.state.fen);
    const move = gameClone.move({ from, to, promotion: "q" }); // Default promotion to queen

    if (move === null) {
      return; // Illegal move
    }

    // Check if it's the user's turn
    const isPlayer1 = user.id === game.player1_id;
    const isPlayer2 = user.id === game.player2_id;
    const turn = gameClone.turn();

    if ((isPlayer1 && turn !== "w") || (isPlayer2 && turn !== "b")) {
      return; // Not your turn
    }

    // Update the game state in Supabase
    const { error } = await supabase
      .from("games")
      .update({
        state: { fen: gameClone.fen(), turn: turn },
        pgn_history: gameClone.pgn(),
      })
      .eq("id", gameId);

    if (error) {
      console.error("Failed to update game state:", error);
      // The UI will eventually be corrected by the real-time subscription
    }
  };

  const handleShare = async () => {
    const gameUrl = `${window.location.origin}/play/${gameId}`;
    try {
      await navigator.clipboard.writeText(gameUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy to clipboard:", err);
    }
  };

  const getPlayerName = (playerId: string | null) => {
    if (!playerId) return "Waiting for player...";
    if (playerId === user?.id) return "You";
    return "Opponent";
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-xl font-semibold text-white">Loading Game...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="text-xl font-semibold text-red-400">{error}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-white">Game Room</h1>
          <button
            onClick={handleShare}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {copied ? (
              <Check className="w-4 h-4" />
            ) : (
              <Share2 className="w-4 h-4" />
            )}
            {copied ? "Copied!" : "Share Game"}
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 flex justify-center">
            <div className="bg-slate-800 rounded-xl p-6 shadow-2xl">
              <ChessBoard
                position={chess.fen()}
                onPieceDrop={handleMove}
                boardOrientation={
                  user?.id === game?.player1_id ? "white" : "black"
                }
                boardTheme={
                  board?.theme
                    ? {
                        lightSquares:
                          (board.theme as any).lightSquareColor || "#f0d9b5",
                        darkSquares:
                          (board.theme as any).darkSquareColor || "#b58863",
                        borderColor: "#374151",
                      }
                    : {
                        lightSquares: "#f0d9b5",
                        darkSquares: "#b58863",
                        borderColor: "#374151",
                      }
                }
                pieceSet="classic"
                customPieces={
                  Object.keys(customPieces).length > 0
                    ? customPieces
                    : undefined
                }
              />
            </div>
          </div>
          <div className="space-y-6">
            {/* Game Info */}
            <div className="bg-slate-800 rounded-lg shadow-xl p-6 border border-slate-700">
              <h2 className="text-xl font-semibold text-white mb-4">
                Game Info
              </h2>
              <div className="space-y-3 text-slate-300">
                <div className="flex justify-between">
                  <span>Board:</span>
                  <span className="text-white font-medium">
                    {board?.name || "Custom Board"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span
                    className={`font-medium capitalize ${
                      game?.status === "playing"
                        ? "text-green-400"
                        : game?.status === "waiting"
                        ? "text-yellow-400"
                        : "text-red-400"
                    }`}
                  >
                    {game?.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>White:</span>
                  <span className="text-white font-medium">
                    {getPlayerName(game?.player1_id || null)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Black:</span>
                  <span className="text-white font-medium">
                    {getPlayerName(game?.player2_id || null)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Turn:</span>
                  <span className="text-white font-medium">
                    {chess.turn() === "w" ? "White" : "Black"}
                  </span>
                </div>
              </div>
            </div>

            {/* Move History */}
            <div className="bg-slate-800 rounded-lg shadow-xl p-6 border border-slate-700">
              <h2 className="text-xl font-semibold text-white mb-4">
                Move History
              </h2>
              <div className="text-slate-300 text-sm font-mono max-h-40 overflow-y-auto">
                {game?.pgn_history || "No moves yet"}
              </div>
            </div>

            {/* Actions */}
            <div className="bg-slate-800 rounded-lg shadow-xl p-6 border border-slate-700">
              <h2 className="text-xl font-semibold text-white mb-4">Actions</h2>
              <div className="space-y-3">
                {game?.status === "waiting" && (
                  <div className="text-slate-300 text-sm">
                    Waiting for another player to join...
                  </div>
                )}
                {game?.status === "playing" && (
                  <div className="space-y-2">
                    <button className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
                      Resign
                    </button>
                    <button className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors">
                      Offer Draw
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
