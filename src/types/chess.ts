export type PieceType = 'king' | 'queen' | 'rook' | 'knight' | 'bishop' | 'pawn'
export type PieceColor = 'white' | 'black'
export type GameStatus = 'waiting' | 'playing' | 'finished'
export type TimerType = 'classic' | 'blitz' | 'none'

export interface ChessPiece {
  id: string
  board_id: string
  type: PieceType
  color: PieceColor
  image_url: string
}

export interface BoardTheme {
  lightSquareColor: string
  darkSquareColor: string
  lightSquareImage?: string
  darkSquareImage?: string
  mode: 'light' | 'dark'
}

export interface ChessBoard {
  id: string
  user_id: string
  name: string
  theme: BoardTheme
  pieces: ChessPiece[]
  created_at: string
}

export interface GameState {
  fen: string
  turn: PieceColor
  castling: {
    whiteKingSide: boolean
    whiteQueenSide: boolean
    blackKingSide: boolean
    blackQueenSide: boolean
  }
  enPassant: string | null
  halfMoveClock: number
  fullMoveNumber: number
}

export interface ChessGame {
  id: string
  board_id: string
  board?: ChessBoard
  player1_id: string
  player2_id: string | null
  state: GameState
  pgn_history: string
  status: GameStatus
  timer_type: TimerType
  timer_duration: number
  created_at: string
  updated_at: string
}

export interface GameMove {
  from: string
  to: string
  promotion?: PieceType
  timestamp: number
  player: PieceColor
}
