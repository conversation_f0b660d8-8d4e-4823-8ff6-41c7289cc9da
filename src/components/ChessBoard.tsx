'use client'

import { useState, useEffect } from 'react'
import { Chessboard } from 'react-chessboard'
import { Chess } from 'chess.js'
import type { Piece, Square } from 'react-chessboard/dist/chessboard/types'

interface ChessBoardProps {
  position: string
  onSquareClick?: (square: string) => void
  onPieceDrop?: (sourceSquare: string, targetSquare: string, piece: string) => void
  selectedSquare?: string | null
  boardOrientation?: 'white' | 'black'
  boardTheme: {
    lightSquares: string
    darkSquares: string
    borderColor: string
  }
  pieceSet: string
}

export function ChessBoard({
  position,
  onSquareClick,
  onPieceDrop,
  selectedSquare,
  boardOrientation,
  boardTheme,
  pieceSet
}: ChessBoardProps) {
  const [game, setGame] = useState(new Chess())
  const [boardPosition, setBoardPosition] = useState(position)

  useEffect(() => {
    try {
      const newGame = new Chess(position)
      setGame(newGame)
      setBoardPosition(position)
    } catch (error) {
      console.error('Invalid position:', error)
    }
  }, [position])

  const handleSquareClick = (square: string) => {
    if (onSquareClick) {
      onSquareClick(square)
    }
  }

  const handlePieceDrop = (sourceSquare: Square, targetSquare: Square, piece: Piece) => {
    if (onPieceDrop) {
      onPieceDrop(sourceSquare, targetSquare, piece)
    }
    // Optimistically allow the move on the frontend.
    // The board state will be corrected by the real-time subscription if the move is invalid.
    return true
  }

  const customSquareStyles = selectedSquare ? {
    [selectedSquare]: {
      backgroundColor: 'rgba(255, 255, 0, 0.4)',
      border: '2px solid #fbbf24'
    }
  } : {}

  const boardThemeStyles = {
    borderRadius: '8px',
    border: `3px solid ${boardTheme.borderColor}`,
  }

  const customLightSquareStyle = { backgroundColor: boardTheme.lightSquares }
  const customDarkSquareStyle = { backgroundColor: boardTheme.darkSquares }

  return (
    <div style={boardThemeStyles}>
      <Chessboard
        position={boardPosition}
        onSquareClick={handleSquareClick}
        onPieceDrop={handlePieceDrop}
        customSquareStyles={customSquareStyles}
        customLightSquareStyle={customLightSquareStyle}
        customDarkSquareStyle={customDarkSquareStyle}
        boardWidth={400}
        animationDuration={200}
        arePiecesDraggable={!!onPieceDrop}
        boardOrientation={boardOrientation}
        showBoardNotation={true}
        customBoardStyle={{
          borderRadius: '4px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
        }}
      />
    </div>
  )
}
