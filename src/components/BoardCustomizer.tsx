'use client'

import { useState } from 'react'

interface BoardTheme {
  lightSquares: string
  darkSquares: string
  borderColor: string
}

interface BoardCustomizerProps {
  boardTheme: BoardTheme
  onThemeChange: (theme: BoardTheme) => void
}

const presetThemes = [
  {
    id: 'classic',
    name: 'Classic',
    theme: {
      lightSquares: '#f0d9b5',
      darkSquares: '#b58863',
      borderColor: '#8b4513'
    }
  },
  {
    id: 'modern',
    name: 'Modern',
    theme: {
      lightSquares: '#f4f4f4',
      darkSquares: '#8b8b8b',
      borderColor: '#4a4a4a'
    }
  },
  {
    id: 'blue',
    name: 'Blue',
    theme: {
      lightSquares: '#e6f3ff',
      darkSquares: '#4a90e2',
      borderColor: '#2c5aa0'
    }
  },
  {
    id: 'green',
    name: 'Green',
    theme: {
      lightSquares: '#e8f5e8',
      darkSquares: '#4caf50',
      borderColor: '#2e7d32'
    }
  },
  {
    id: 'purple',
    name: 'Purple',
    theme: {
      lightSquares: '#f3e5f5',
      darkSquares: '#9c27b0',
      borderColor: '#6a1b9a'
    }
  },
  {
    id: 'dark',
    name: 'Dark',
    theme: {
      lightSquares: '#4a4a4a',
      darkSquares: '#2d2d2d',
      borderColor: '#1a1a1a'
    }
  }
]

export function BoardCustomizer({
  boardTheme,
  onThemeChange
}: BoardCustomizerProps) {
  const [activePreset, setActivePreset] = useState<string>('classic')

  const handlePresetChange = (presetId: string) => {
    const preset = presetThemes.find(p => p.id === presetId)
    if (preset) {
      setActivePreset(presetId)
      onThemeChange(preset.theme)
    }
  }

  const handleColorChange = (property: keyof BoardTheme, value: string) => {
    onThemeChange({
      ...boardTheme,
      [property]: value
    })
  }

  return (
    <div className="space-y-4">
      {/* Preset Themes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Preset Themes
        </label>
        <div className="grid grid-cols-2 gap-2">
          {presetThemes.map((preset) => (
            <button
              key={preset.id}
              onClick={() => handlePresetChange(preset.id)}
              className={`p-2 text-xs font-medium rounded-lg border-2 transition-all ${
                activePreset === preset.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 text-gray-700 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Colors */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Custom Colors
        </label>
        
        {/* Light Squares */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Light Squares
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={boardTheme.lightSquares}
              onChange={(e) => handleColorChange('lightSquares', e.target.value)}
              className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={boardTheme.lightSquares}
              onChange={(e) => handleColorChange('lightSquares', e.target.value)}
              className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="#f0d9b5"
            />
          </div>
        </div>

        {/* Dark Squares */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Dark Squares
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={boardTheme.darkSquares}
              onChange={(e) => handleColorChange('darkSquares', e.target.value)}
              className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={boardTheme.darkSquares}
              onChange={(e) => handleColorChange('darkSquares', e.target.value)}
              className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="#b58863"
            />
          </div>
        </div>

        {/* Border Color */}
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Border Color
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="color"
              value={boardTheme.borderColor}
              onChange={(e) => handleColorChange('borderColor', e.target.value)}
              className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
            />
            <input
              type="text"
              value={boardTheme.borderColor}
              onChange={(e) => handleColorChange('borderColor', e.target.value)}
              className="flex-1 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="#8b4513"
            />
          </div>
        </div>
      </div>

      {/* Preview */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Preview
        </label>
        <div 
          className="w-full h-16 rounded-lg border-2"
          style={{
            background: `linear-gradient(45deg, ${boardTheme.lightSquares} 25%, transparent 25%), 
                        linear-gradient(-45deg, ${boardTheme.lightSquares} 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, ${boardTheme.lightSquares} 75%), 
                        linear-gradient(-45deg, transparent 75%, ${boardTheme.lightSquares} 75%)`,
            backgroundSize: '16px 16px',
            backgroundPosition: '0 0, 0 8px, 8px -8px, -8px 0px',
            backgroundColor: boardTheme.darkSquares,
            borderColor: boardTheme.borderColor
          }}
        />
      </div>
    </div>
  )
} 