"use client";
// @ts-nocheck - Disabling for this file due to a temporary type generation caching issue.

import { ChangeEvent, useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Database } from "@/types/database";
import { PIECE_TYPES, Piece, PIECES, PieceInfo } from "@/lib/constants";
import {
  Upload,
  Sparkles,
  Loader2,
  Trash2,
  GripVertical,
  Image as ImageIcon,
  X,
} from "lucide-react";
import {
  DndContext,
  closestCenter,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
} from "@dnd-kit/core";
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

type CustomImage = Database["public"]["Tables"]["custom_images"]["Row"];
type PieceRole = `w${Piece}` | `b${Piece}`;

// Main component that orchestrates everything
export function ImageManager({
  customPieces,
  onCustomPiecesChange,
}: {
  customPieces: Record<string, string>;
  onCustomPiecesChange: (pieces: Record<string, string>) => void;
}) {
  const [images, setImages] = useState<CustomImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeDragId, setActiveDragId] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchImages = async () => {
      setIsLoading(true);
      const response = await fetch("/api/custom-images");
      if (response.ok) {
        const data = await response.json();
        setImages(data);
      }
      setIsLoading(false);
    };
    fetchImages();
  }, []);

  const handleImageUploaded = (newImage: CustomImage) => {
    setImages((prev) => [newImage, ...prev]);
  };

  const handleDeleteImage = async (imageId: string) => {
    setImages((prev) => prev.filter((img) => img.id !== imageId));
    await fetch(`/api/custom-images/${imageId}`, { method: "DELETE" });
    // Un-assign from any role it was used in
    const newPieces = { ...customPieces };
    Object.entries(newPieces).forEach(([role, url]) => {
      const image = images.find((img) => img.id === imageId);
      if (image && image.image_url === url) {
        delete (newPieces as any)[role];
      }
    });
    onCustomPiecesChange(newPieces);
  };

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragStart = (event: DragStartEvent) =>
    setActiveDragId(event.active.id as string);

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveDragId(null);
    const { active, over } = event;
    if (!over) return;

    const activeImage = images.find((img) => img.id === active.id);
    if (!activeImage) return;

    const overId = over.id as string;

    if (overId.startsWith("droppable-")) {
      const role = overId.replace("droppable-", "");

      // Check if this image is already assigned to another piece
      const existingAssignment = Object.entries(customPieces).find(
        ([existingRole, imageUrl]) =>
          imageUrl === activeImage.image_url && existingRole !== role
      );

      if (existingAssignment) {
        // Show error message and prevent assignment
        setErrorMessage(
          `This image is already assigned to ${existingAssignment[0]}. Please choose a different image or unassign it first.`
        );
        // Clear error after 5 seconds
        setTimeout(() => setErrorMessage(null), 5000);
        return;
      }

      onCustomPiecesChange({ ...customPieces, [role]: activeImage.image_url });
      // Clear any existing error message on successful assignment
      setErrorMessage(null);
    } else {
      // Reordering logic for the image library
      const activeIndex = images.findIndex((img) => img.id === active.id);
      const overIndex = images.findIndex((img) => img.id === over.id);
      if (activeIndex !== overIndex) {
        setImages(arrayMove(images, activeIndex, overIndex));
      }
    }
  };

  const activeImage = images.find((img) => img.id === activeDragId);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-6">
        {/* Error Message */}
        {errorMessage && (
          <div className="bg-red-600 text-white p-3 rounded-lg flex items-center gap-2">
            <span className="text-red-200">⚠️</span>
            <span>{errorMessage}</span>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Uploader onImageUploaded={handleImageUploaded} />
          <Generator onImageGenerated={handleImageUploaded} />
        </div>
        <div className="p-4 bg-gray-900/50 rounded-lg">
          <h3 className="text-xl font-bold text-white mb-4">
            Image Library & Assignments
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <h4 className="font-semibold text-white mb-2">
                Available Images
              </h4>
              <p className="text-xs text-gray-400 mb-4">
                Drag an image onto a piece role to assign it.
              </p>
              <ImageGallery
                images={images}
                isLoading={isLoading}
                onDelete={handleDeleteImage}
              />
            </div>
            <div className="lg:col-span-2">
              <h4 className="font-semibold text-white mb-2">
                Piece Role Assignments
              </h4>
              <p className="text-xs text-gray-400 mb-4">
                Drop an image here to assign it to a role.
              </p>
              <AssignmentGrid
                assignments={customPieces}
                onCustomPiecesChange={onCustomPiecesChange}
              />
            </div>
          </div>
        </div>
      </div>
      <DragOverlay>
        {activeDragId ? <DraggableImage image={activeImage} /> : null}
      </DragOverlay>
    </DndContext>
  );
}

function Uploader({
  onImageUploaded,
}: {
  onImageUploaded: (image: CustomImage) => void;
}) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    setError(null);

    const formData = new FormData();
    formData.append("image", file);

    try {
      const response = await fetch("/api/custom-images", {
        method: "POST",
        body: formData,
      });
      if (!response.ok) {
        const err = await response.json();
        throw new Error(err.error || "Upload failed");
      }
      const newImage = await response.json();
      onImageUploaded(newImage);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="p-4 bg-gray-900/50 rounded-lg h-full">
      <h4 className="text-lg font-semibold text-white mb-2">Upload Image</h4>
      <label className="relative flex flex-col items-center justify-center w-full h-32 border-2 border-gray-600 border-dashed rounded-lg cursor-pointer hover:bg-gray-800/50 transition-colors">
        {isUploading ? (
          <>
            <Loader2 className="h-8 w-8 animate-spin text-purple-400" />
            <span className="mt-2 text-sm font-semibold text-gray-300">
              Uploading...
            </span>
          </>
        ) : (
          <>
            <Upload className="w-8 h-8 text-gray-400" />
            <span className="mt-2 text-sm font-semibold text-gray-300">
              Click to upload
            </span>
            <span className="text-xs text-gray-500">PNG or WEBP (Max 2MB)</span>
          </>
        )}
        <input
          type="file"
          className="hidden"
          onChange={handleFileChange}
          accept="image/png, image/webp"
          disabled={isUploading}
        />
      </label>
      {error && <p className="text-red-500 text-xs mt-2">{error}</p>}
    </div>
  );
}

function Generator({
  onImageGenerated,
}: {
  onImageGenerated: (image: CustomImage) => void;
}) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [prompt, setPrompt] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch("/api/generate-piece", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
      });

      if (!response.ok) {
        const err = await response.json();
        throw new Error(err.error || "Generation failed.");
      }
      const newImage = await response.json();
      onImageGenerated(newImage);
      setPrompt("");
    } catch (e: any) {
      setError(e.message);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="p-4 bg-gray-900/50 rounded-lg h-full">
      <h4 className="text-lg font-semibold text-white mb-2">
        Generate AI Image
      </h4>
      <div className="flex flex-col h-32">
        <input
          type="text"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="e.g., cyberpunk style, neon king"
          className="flex-grow w-full bg-gray-700 border border-gray-600 text-white rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:outline-none"
          disabled={isGenerating}
        />
        <button
          onClick={handleGenerate}
          disabled={isGenerating || !prompt.trim()}
          className="mt-2 w-full flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 text-white font-semibold rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isGenerating ? (
            <Loader2 className="animate-spin" size={20} />
          ) : (
            <Sparkles size={20} />
          )}
          <span>{isGenerating ? "Generating..." : "Generate"}</span>
        </button>
      </div>
      {error && <p className="text-red-500 text-xs mt-2">{error}</p>}
    </div>
  );
}

function ImageGallery({
  images,
  isLoading,
  onDelete,
}: {
  images: CustomImage[];
  isLoading: boolean;
  onDelete: (id: string) => void;
}) {
  return (
    <div className="h-96 overflow-y-auto bg-gray-900 rounded-lg p-2 space-y-2">
      {isLoading && (
        <div className="flex justify-center items-center h-full">
          <Loader2 className="animate-spin" />
        </div>
      )}
      {!isLoading && images.length === 0 && (
        <div className="text-center text-gray-500 py-10">
          <ImageIcon className="mx-auto h-12 w-12" />
          <p className="mt-2">Your library is empty.</p>
          <p className="text-xs">Upload or generate an image to get started.</p>
        </div>
      )}
      <SortableContext
        items={images.map((i) => i.id)}
        strategy={verticalListSortingStrategy}
      >
        {images.map((image) => (
          <DraggableImage key={image.id} image={image} onDelete={onDelete} />
        ))}
      </SortableContext>
    </div>
  );
}

function DraggableImage({
  image,
  onDelete,
}: {
  image: CustomImage | undefined;
  onDelete?: (id: string) => void;
}) {
  if (!image) return null;
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: image.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="p-2 bg-gray-800 rounded-md flex items-center gap-2 touch-none"
    >
      <div
        {...listeners}
        className="cursor-grab px-1 text-gray-500 hover:text-white"
      >
        <GripVertical />
      </div>
      <img
        src={image.image_url}
        alt={image.name || "custom piece"}
        className="w-10 h-10 object-contain bg-gray-700 rounded-md"
      />
      <span className="flex-grow text-sm text-gray-300 truncate">
        {image.name}
      </span>
      {onDelete && (
        <button
          onClick={() => onDelete(image.id)}
          className="text-gray-500 hover:text-red-500 pr-1"
        >
          <Trash2 size={16} />
        </button>
      )}
    </div>
  );
}

function AssignmentGrid({
  assignments,
  onCustomPiecesChange,
}: {
  assignments: Record<string, string>;
  onCustomPiecesChange: (pieces: Record<string, string>) => void;
}) {
  const handleUnassign = (role: string) => {
    const newAssignments = { ...assignments };
    delete (newAssignments as any)[role];
    onCustomPiecesChange(newAssignments);
  };

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
      {PIECES.map((piece: PieceInfo) => {
        const pieceType = piece.type === "knight" ? "n" : piece.type[0];
        const role: PieceRole = `${
          piece.color
        }${pieceType.toUpperCase()}` as PieceRole;
        return (
          <div key={role}>
            <h5 className="text-center text-sm font-bold text-gray-400 mb-1">
              {piece.name}
            </h5>
            <DroppableSlot
              role={role}
              imageUrl={assignments[role]}
              onUnassign={() => handleUnassign(role)}
            />
          </div>
        );
      })}
    </div>
  );
}

function DroppableSlot({
  role,
  imageUrl,
  onUnassign,
}: {
  role: string;
  imageUrl: string | undefined;
  onUnassign: () => void;
}) {
  const { setNodeRef } = useSortable({ id: `droppable-${role}` });

  return (
    <div
      ref={setNodeRef}
      className={`aspect-square w-full rounded-lg flex items-center justify-center relative transition-colors
            ${
              imageUrl
                ? "bg-gray-700"
                : "bg-gray-800 border-2 border-dashed border-gray-700"
            }`}
    >
      {imageUrl ? (
        <>
          <img
            src={imageUrl}
            alt={role}
            className="w-full h-full object-contain p-1"
          />
          <button
            onClick={onUnassign}
            className="absolute -top-1 -right-1 bg-red-600 rounded-full p-0.5 text-white hover:bg-red-500"
          >
            <X size={12} />
          </button>
        </>
      ) : (
        <span className="text-gray-600 text-3xl">
          {
            PIECES.find((p: PieceInfo) => {
              const type = p.type === "knight" ? "n" : p.type[0];
              return `${p.color}${type.toUpperCase()}` === role;
            })?.unicode
          }
        </span>
      )}
    </div>
  );
}
