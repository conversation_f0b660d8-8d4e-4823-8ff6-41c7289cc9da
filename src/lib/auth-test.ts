import { supabase } from './supabase'

export async function testAuthFlow() {
  console.log('Testing Supabase Auth connection...')
  
  try {
    // Test 1: Check if we can get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      console.error('Session check failed:', sessionError)
      return { success: false, error: sessionError.message }
    }
    
    console.log('Session check successful:', session ? 'User logged in' : 'No active session')
    
    // Test 2: Check if we can access the users table (should work with RLS)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .limit(1)
    
    if (userError) {
      console.log('Users table access (expected to fail without auth):', userError.message)
    } else {
      console.log('Users table accessible:', userData)
    }
    
    // Test 3: Check if we can access board themes (public table)
    const { data: themesData, error: themesError } = await supabase
      .from('board_themes')
      .select('*')
      .limit(3)
    
    if (themesError) {
      console.error('Board themes access failed:', themesError)
      return { success: false, error: themesError.message }
    }
    
    console.log('Board themes accessible:', themesData)
    
    return { 
      success: true, 
      session: !!session,
      themesCount: themesData?.length || 0
    }
    
  } catch (err) {
    console.error('Auth test failed:', err)
    return { success: false, error: 'Connection failed' }
  }
}

// Test function for creating a test user (use with caution)
export async function createTestUser(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })
    
    if (error) {
      console.error('Test user creation failed:', error)
      return { success: false, error: error.message }
    }
    
    console.log('Test user created:', data)
    return { success: true, user: data.user }
    
  } catch (err) {
    console.error('Test user creation error:', err)
    return { success: false, error: 'Creation failed' }
  }
}
