import { supabase } from './supabase'

export async function testSupabaseConnection() {
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('board_themes')
      .select('*')
      .limit(1)

    if (error) {
      console.error('Supabase connection error:', error)
      return { success: false, error: error.message }
    }

    console.log('Supabase connection successful:', data)
    return { success: true, data }
  } catch (err) {
    console.error('Supabase test failed:', err)
    return { success: false, error: 'Connection failed' }
  }
}

export async function testSupabaseAuth() {
  try {
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('Auth test error:', error)
      return { success: false, error: error.message }
    }

    return { success: true, session }
  } catch (err) {
    console.error('Auth test failed:', err)
    return { success: false, error: 'Auth test failed' }
  }
}
