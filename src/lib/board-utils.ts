import { SupabaseClient } from "@supabase/supabase-js";

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface AIGenerationResult {
  success: boolean;
  urls?: string[];
  error?: string;
}

// Upload image to Supabase Storage
export async function uploadPieceImage(
  supabase: SupabaseClient,
  file: File,
  pieceType: string,
  pieceColor: "white" | "black"
): Promise<UploadResult> {
  try {
    // Validate file
    if (!file.type.startsWith("image/")) {
      return {
        success: false,
        error: "Invalid file type. Please upload an image.",
      };
    }

    if (file.size > 5 * 1024 * 1024) {
      return { success: false, error: "File size must be less than 5MB." };
    }

    // Create unique filename
    const fileExt = file.name.split(".").pop();
    const fileName = `${pieceColor}_${pieceType}_${Date.now()}.${fileExt}`;
    const filePath = `pieces/${fileName}`;

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from("chess-pieces")
      .upload(filePath, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) {
      console.error("Upload error:", error);
      return {
        success: false,
        error: "Failed to upload image. Please try again.",
      };
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from("chess-pieces")
      .getPublicUrl(filePath);

    return { success: true, url: urlData.publicUrl };
  } catch (error) {
    console.error("Upload error:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Generate AI image using OpenAI
export async function generateAIPiece(
  pieceType: string,
  pieceColor: "white" | "black",
  prompt: string
): Promise<AIGenerationResult> {
  try {
    const enhancedPrompt = `Create a 128x128 square icon of a ${pieceColor} ${pieceType} chess piece with a transparent background. Style: ${prompt}. No text, just the chess piece icon.`;

    // Call OpenAI API (this would be done through a Supabase Edge Function)
    const response = await fetch("/api/generate-piece", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        prompt: enhancedPrompt,
        pieceType,
        pieceColor,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to generate image");
    }

    const result = await response.json();

    if (result.success) {
      return { success: true, urls: result.urls };
    } else {
      return {
        success: false,
        error: result.error || "Failed to generate image",
      };
    }
  } catch (error) {
    console.error("AI generation error:", error);
    return {
      success: false,
      error: "Failed to generate AI image. Please try again.",
    };
  }
}

// Save board to database
export async function saveBoard(
  supabase: SupabaseClient,
  userId: string,
  boardName: string,
  boardTheme: any,
  customPieces: Record<string, string>
) {
  try {
    // Save board metadata
    const { data: boardData, error: boardError } = await supabase
      .from("boards")
      .insert({
        user_id: userId,
        name: boardName,
        theme: boardTheme,
      })
      .select()
      .single();

    if (boardError) throw boardError;

    // Save custom pieces
    if (Object.keys(customPieces).length > 0) {
      const pieceRecords = Object.entries(customPieces).map(
        ([pieceKey, imageUrl]) => {
          const color = pieceKey.startsWith("w") ? "white" : "black";
          const pieceChar = pieceKey.slice(1).toLowerCase();

          // Convert piece character to full name for database enum
          const pieceTypeMap: Record<string, string> = {
            k: "king",
            q: "queen",
            r: "rook",
            n: "knight",
            b: "bishop",
            p: "pawn",
          };

          const type = pieceTypeMap[pieceChar];
          if (!type) {
            throw new Error(`Invalid piece type: ${pieceChar}`);
          }

          return {
            board_id: boardData.id,
            type,
            color,
            image_url: imageUrl,
          };
        }
      );

      const { error: piecesError } = await supabase
        .from("pieces")
        .insert(pieceRecords);

      if (piecesError) throw piecesError;
    }

    return { success: true, boardId: boardData.id };
  } catch (error) {
    console.error("Save board error:", error);
    return { success: false, error: "Failed to save board" };
  }
}
