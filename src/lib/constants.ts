import { PieceType, PieceColor, TimerType } from '@/types/chess'

export const PIECE_TYPES = ['king', 'queen', 'rook', 'bishop', 'knight', 'pawn'] as const
export type Piece = typeof PIECE_TYPES[number]

export const PIECE_COLORS: PieceColor[] = ['white', 'black']

export const TIMER_OPTIONS: { label: string; value: TimerType; duration: number }[] = [
  { label: 'Classic (10 min)', value: 'classic', duration: 600 },
  { label: 'Blitz (5 min)', value: 'blitz', duration: 300 },
  { label: 'No Timer', value: 'none', duration: 0 },
]

export const DEFAULT_BOARD_THEME = {
  lightSquareColor: '#f0d9b5',
  darkSquareColor: '#b58863',
  mode: 'light' as const,
}

export const INITIAL_FEN = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'

export const PIECE_UNICODE = {
  white: {
    king: '♔',
    queen: '♕',
    rook: '♖',
    knight: '♘',
    bishop: '♗',
    pawn: '♙',
  },
  black: {
    king: '♚',
    queen: '♛',
    rook: '♜',
    knight: '♞',
    bishop: '♝',
    pawn: '♟',
  },
}

export const SQUARE_NAMES = [
  'a8', 'b8', 'c8', 'd8', 'e8', 'f8', 'g8', 'h8',
  'a7', 'b7', 'c7', 'd7', 'e7', 'f7', 'g7', 'h7',
  'a6', 'b6', 'c6', 'd6', 'e6', 'f6', 'g6', 'h6',
  'a5', 'b5', 'c5', 'd5', 'e5', 'f5', 'g5', 'h5',
  'a4', 'b4', 'c4', 'd4', 'e4', 'f4', 'g4', 'h4',
  'a3', 'b3', 'c3', 'd3', 'e3', 'f3', 'g3', 'h3',
  'a2', 'b2', 'c2', 'd2', 'e2', 'f2', 'g2', 'h2',
  'a1', 'b1', 'c1', 'd1', 'e1', 'f1', 'g1', 'h1',
]

export interface PieceInfo {
    color: 'w' | 'b',
    type: Piece,
    name: string,
    unicode: string
}

export const PIECES: PieceInfo[] = [
    { color: 'w', type: 'king', name: 'White King', unicode: '♔' },
    { color: 'w', type: 'queen', name: 'White Queen', unicode: '♕' },
    { color: 'w', type: 'rook', name: 'White Rook', unicode: '♖' },
    { color: 'w', type: 'bishop', name: 'White Bishop', unicode: '♗' },
    { color: 'w', type: 'knight', name: 'White Knight', unicode: '♘' },
    { color: 'w', type: 'pawn', name: 'White Pawn', unicode: '♙' },
    { color: 'b', type: 'king', name: 'Black King', unicode: '♚' },
    { color: 'b', type: 'queen', name: 'Black Queen', unicode: '♛' },
    { color: 'b', type: 'rook', name: 'Black Rook', unicode: '♜' },
    { color: 'b', type: 'bishop', name: 'Black Bishop', unicode: '♝' },
    { color: 'b', type: 'knight', name: 'Black Knight', unicode: '♞' },
    { color: 'b', type: 'pawn', name: 'Black Pawn', unicode: '♟' },
]

export const BOARD_THEMES = {
  // ... existing code ...
}
