import { supabase } from './supabase'

/**
 * Test function for forgot password functionality
 * This function tests the resetPasswordForEmail method
 * 
 * @param email - The email address to send the reset link to
 * @returns Promise with success/error status
 */
export async function testForgotPassword(email: string) {
  try {
    console.log(`Testing forgot password for email: ${email}`)
    
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
    })
    
    if (error) {
      console.error('Forgot password test failed:', error)
      return { success: false, error: error.message }
    }
    
    console.log('Forgot password test successful - email should be sent')
    return { success: true, message: 'Password reset email sent successfully' }
    
  } catch (err) {
    console.error('Forgot password test error:', err)
    return { success: false, error: 'Test failed with unexpected error' }
  }
}

/**
 * Test function for password update functionality
 * This function tests the updateUser method for password updates
 * Note: This requires an authenticated session
 * 
 * @param newPassword - The new password to set
 * @returns Promise with success/error status
 */
export async function testPasswordUpdate(newPassword: string) {
  try {
    console.log('Testing password update...')
    
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return { success: false, error: 'User must be authenticated to update password' }
    }
    
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    })
    
    if (error) {
      console.error('Password update test failed:', error)
      return { success: false, error: error.message }
    }
    
    console.log('Password update test successful')
    return { success: true, message: 'Password updated successfully' }
    
  } catch (err) {
    console.error('Password update test error:', err)
    return { success: false, error: 'Test failed with unexpected error' }
  }
}

/**
 * Test function to verify auth context methods
 * This function tests if the auth context methods are properly exposed
 */
export function testAuthContextMethods() {
  // This would be used in a React component context
  console.log('Auth context methods that should be available:')
  console.log('- resetPassword(email: string)')
  console.log('- updatePassword(password: string)')
  console.log('These methods should be accessible via useAuth() hook')
  
  return {
    success: true,
    message: 'Auth context methods are properly defined'
  }
}
