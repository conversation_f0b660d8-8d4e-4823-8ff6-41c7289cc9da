-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE game_status AS ENUM ('waiting', 'playing', 'finished');
CREATE TYPE timer_type AS ENUM ('classic', 'blitz', 'none');
CREATE TYPE piece_type AS ENUM ('king', 'queen', 'rook', 'knight', 'bishop', 'pawn');
CREATE TYPE piece_color AS ENUM ('white', 'black');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Boards table
CREATE TABLE public.boards (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    theme JSONB NOT NULL DEFAULT '{"lightSquareColor": "#f0d9b5", "darkSquareColor": "#b58863", "mode": "light"}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pieces table
CREATE TABLE public.pieces (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    board_id UUID REFERENCES public.boards(id) ON DELETE CASCADE NOT NULL,
    type piece_type NOT NULL,
    color piece_color NOT NULL,
    image_url TEXT NOT NULL
);

-- Games table
CREATE TABLE public.games (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    board_id UUID REFERENCES public.boards(id) ON DELETE CASCADE NOT NULL,
    player1_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    player2_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    state JSONB NOT NULL DEFAULT '{"fen": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", "turn": "white"}',
    pgn_history TEXT DEFAULT '',
    status game_status DEFAULT 'waiting',
    timer_type timer_type DEFAULT 'classic',
    timer_duration INTEGER DEFAULT 600, -- seconds
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_boards_user_id ON public.boards(user_id);
CREATE INDEX idx_pieces_board_id ON public.pieces(board_id);
CREATE INDEX idx_games_board_id ON public.games(board_id);
CREATE INDEX idx_games_player1_id ON public.games(player1_id);
CREATE INDEX idx_games_player2_id ON public.games(player2_id);
CREATE INDEX idx_games_status ON public.games(status);

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.boards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pieces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.games ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Boards policies
CREATE POLICY "Users can view their own boards" ON public.boards
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own boards" ON public.boards
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own boards" ON public.boards
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own boards" ON public.boards
    FOR DELETE USING (auth.uid() = user_id);

-- Pieces policies
CREATE POLICY "Users can view pieces of their boards" ON public.pieces
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.boards 
            WHERE boards.id = pieces.board_id 
            AND boards.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create pieces for their boards" ON public.pieces
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.boards 
            WHERE boards.id = pieces.board_id 
            AND boards.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update pieces of their boards" ON public.pieces
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.boards 
            WHERE boards.id = pieces.board_id 
            AND boards.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete pieces of their boards" ON public.pieces
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.boards 
            WHERE boards.id = pieces.board_id 
            AND boards.user_id = auth.uid()
        )
    );

-- Games policies
CREATE POLICY "Users can view games they participate in" ON public.games
    FOR SELECT USING (
        auth.uid() = player1_id OR 
        auth.uid() = player2_id OR
        EXISTS (
            SELECT 1 FROM public.boards 
            WHERE boards.id = games.board_id 
            AND boards.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create games with their boards" ON public.games
    FOR INSERT WITH CHECK (
        auth.uid() = player1_id AND
        EXISTS (
            SELECT 1 FROM public.boards 
            WHERE boards.id = games.board_id 
            AND boards.user_id = auth.uid()
        )
    );

CREATE POLICY "Players can update games they participate in" ON public.games
    FOR UPDATE USING (
        auth.uid() = player1_id OR auth.uid() = player2_id
    );

-- Function to automatically create user profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email)
    VALUES (NEW.id, NEW.email);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at on games
CREATE TRIGGER update_games_updated_at
    BEFORE UPDATE ON public.games
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
