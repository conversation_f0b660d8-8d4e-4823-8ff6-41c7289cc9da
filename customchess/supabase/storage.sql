-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('piece-images', 'piece-images', true),
    ('board-backgrounds', 'board-backgrounds', true);

-- Storage policies for piece-images bucket
CREATE POLICY "Users can upload piece images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'piece-images' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Anyone can view piece images" ON storage.objects
    FOR SELECT USING (bucket_id = 'piece-images');

CREATE POLICY "Users can update their own piece images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'piece-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own piece images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'piece-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Storage policies for board-backgrounds bucket
CREATE POLICY "Users can upload board backgrounds" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'board-backgrounds' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Anyone can view board backgrounds" ON storage.objects
    FOR SELECT USING (bucket_id = 'board-backgrounds');

CREATE POLICY "Users can update their own board backgrounds" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'board-backgrounds' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own board backgrounds" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'board-backgrounds' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );
