{"name": "customchess", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.0", "@types/uuid": "^10.0.0", "chess.js": "^1.4.0", "clsx": "^2.1.1", "lucide-react": "^0.522.0", "next": "^13.5.6", "react": "^19.0.0", "react-chessboard": "^4.7.3", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "^13.5.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}