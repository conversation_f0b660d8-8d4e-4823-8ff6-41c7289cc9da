{"version": 3, "sources": ["../../src/server/internal-utils.ts"], "names": ["stripInternalQueries", "stripInternalSearchParams", "stripInternalHeaders", "INTERNAL_QUERY_NAMES", "NEXT_RSC_UNION_QUERY", "EDGE_EXTENDED_INTERNAL_QUERY_NAMES", "query", "name", "url", "isEdge", "isStringUrl", "instance", "URL", "searchParams", "delete", "toString", "INTERNAL_HEADERS", "headers", "key"], "mappings": ";;;;;;;;;;;;;;;;IAgBgBA,oBAAoB;eAApBA;;IAMAC,yBAAyB;eAAzBA;;IAqCAC,oBAAoB;eAApBA;;;kCAxDqB;AAErC,MAAMC,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;IACAC,sCAAoB;CACrB;AAED,MAAMC,qCAAqC;IAAC;CAAgB;AAErD,SAASL,qBAAqBM,KAAyB;IAC5D,KAAK,MAAMC,QAAQJ,qBAAsB;QACvC,OAAOG,KAAK,CAACC,KAAK;IACpB;AACF;AAEO,SAASN,0BACdO,GAAM,EACNC,MAAe;IAEf,MAAMC,cAAc,OAAOF,QAAQ;IACnC,MAAMG,WAAWD,cAAc,IAAIE,IAAIJ,OAAQA;IAC/C,KAAK,MAAMD,QAAQJ,qBAAsB;QACvCQ,SAASE,YAAY,CAACC,MAAM,CAACP;IAC/B;IAEA,IAAIE,QAAQ;QACV,KAAK,MAAMF,QAAQF,mCAAoC;YACrDM,SAASE,YAAY,CAACC,MAAM,CAACP;QAC/B;IACF;IAEA,OAAQG,cAAcC,SAASI,QAAQ,KAAKJ;AAC9C;AAEA;;;CAGC,GACD,MAAMK,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAOM,SAASd,qBAAqBe,OAA4B;IAC/D,KAAK,MAAMC,OAAOF,iBAAkB;QAClC,OAAOC,OAAO,CAACC,IAAI;IACrB;AACF"}