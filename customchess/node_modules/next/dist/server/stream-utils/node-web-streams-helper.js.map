{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "names": ["streamToBufferedResult", "cloneTransformStream", "chainStreams", "streamFromString", "streamToString", "createBufferedTransformStream", "createInsertedHTMLStream", "renderToInitialFizzStream", "createRootLayoutValidatorStream", "continueFizzStream", "queueTask", "process", "env", "NEXT_RUNTIME", "globalThis", "setTimeout", "setImmediate", "renderResult", "textDecoder", "TextDecoder", "concatenatedString", "writable", "write", "chunk", "decodeText", "end", "on", "off", "pipe", "source", "sourceReader", "readable", "<PERSON><PERSON><PERSON><PERSON>", "clone", "TransformStream", "start", "controller", "done", "value", "read", "enqueue", "transform", "streams", "promise", "Promise", "resolve", "i", "length", "then", "pipeTo", "preventClose", "str", "ReadableStream", "encodeText", "close", "stream", "reader", "bufferedString", "bufferedBytes", "Uint8Array", "pendingFlush", "flushBuffer", "newBufferedBytes", "byteLength", "set", "flush", "getServerInsertedHTML", "insertedHTMLChunk", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "insertion", "content", "index", "indexOf", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "suffixFlushed", "suffixFlushTask", "res", "createMergedTransformStream", "dataStream", "dataStreamFinished", "dataStreamReader", "err", "error", "createMoveSuffixStream", "foundSuffix", "endsWith", "contentWithoutSuffix", "assetPrefix", "getTree", "foundHtml", "foundBody", "includes", "missingTags", "filter", "nonNullable", "JSON", "stringify", "tree", "renderStream", "inlinedDataStream", "generateStaticHTML", "serverInsertedHTMLToHead", "validateRootLayout", "closeTag", "suffixUnclosed", "split", "allReady", "transforms", "reduce", "pipeThrough"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAeaA,sBAAsB;eAAtBA;;IAoBGC,oBAAoB;eAApBA;;IAmBAC,YAAY;eAAZA;;IAeAC,gBAAgB;eAAhBA;;IASMC,cAAc;eAAdA;;IAmBNC,6BAA6B;eAA7BA;;IAuCAC,wBAAwB;eAAxBA;;IAYAC,yBAAyB;eAAzBA;;IAwLAC,+BAA+B;eAA/BA;;IA0CMC,kBAAkB;eAAlBA;;;6BAnXM;wBACF;2BACI;8BACS;AAEvC,MAAMC,YACJC,QAAQC,GAAG,CAACC,YAAY,KAAK,SAASC,WAAWC,UAAU,GAAGC;AAMzD,MAAMhB,yBAAyB,OACpCiB;IAEA,MAAMC,cAAc,IAAIC;IACxB,IAAIC,qBAAqB;IAEzB,MAAMC,WAAW;QACfC,OAAMC,KAAU;YACdH,sBAAsBI,IAAAA,wBAAU,EAACD,OAAOL;QAC1C;QACAO,QAAO;QAEP,wCAAwC;QACxCC,OAAM;QACNC,QAAO;IACT;IACA,MAAMV,aAAaW,IAAI,CAACP;IACxB,OAAOD;AACT;AAEO,SAASnB,qBAAqB4B,MAAuB;IAC1D,MAAMC,eAAeD,OAAOE,QAAQ,CAACC,SAAS;IAC9C,MAAMC,QAAQ,IAAIC,gBAAgB;QAChC,MAAMC,OAAMC,UAAU;YACpB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMR,aAAaS,IAAI;gBAC/C,IAAIF,MAAM;oBACR;gBACF;gBACAD,WAAWI,OAAO,CAACF;YACrB;QACF;QACA,wBAAwB;QACxBG,cAAa;IACf;IAEA,OAAOR;AACT;AAEO,SAAS/B,aACdwC,OAA4B;IAE5B,MAAM,EAAEX,QAAQ,EAAEV,QAAQ,EAAE,GAAG,IAAIa;IAEnC,IAAIS,UAAUC,QAAQC,OAAO;IAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,QAAQK,MAAM,EAAE,EAAED,EAAG;QACvCH,UAAUA,QAAQK,IAAI,CAAC,IACrBN,OAAO,CAACI,EAAE,CAACG,MAAM,CAAC5B,UAAU;gBAAE6B,cAAcJ,IAAI,IAAIJ,QAAQK,MAAM;YAAC;IAEvE;IAEA,OAAOhB;AACT;AAEO,SAAS5B,iBAAiBgD,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBjB,OAAMC,UAAU;YACdA,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAACF;YAC9Bf,WAAWkB,KAAK;QAClB;IACF;AACF;AAEO,eAAelD,eACpBmD,MAAkC;IAElC,MAAMC,SAASD,OAAOvB,SAAS;IAC/B,MAAMd,cAAc,IAAIC;IAExB,IAAIsC,iBAAiB;IAErB,MAAO,KAAM;QACX,MAAM,EAAEpB,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMkB,OAAOjB,IAAI;QAEzC,IAAIF,MAAM;YACR,OAAOoB;QACT;QAEAA,kBAAkBjC,IAAAA,wBAAU,EAACc,OAAOpB;IACtC;AACF;AAEO,SAASb;IAId,IAAIqD,gBAA4B,IAAIC;IACpC,IAAIC,eAAqC;IAEzC,MAAMC,cAAc,CAACzB;QACnB,IAAI,CAACwB,cAAc;YACjBA,eAAe,IAAIhB,QAAQ,CAACC;gBAC1BnC,UAAU;oBACR0B,WAAWI,OAAO,CAACkB;oBACnBA,gBAAgB,IAAIC;oBACpBC,eAAe;oBACff;gBACF;YACF;QACF;IACF;IAEA,OAAO,IAAIX,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzB,MAAM0B,mBAAmB,IAAIH,WAC3BD,cAAcX,MAAM,GAAGxB,MAAMwC,UAAU;YAEzCD,iBAAiBE,GAAG,CAACN;YACrBI,iBAAiBE,GAAG,CAACzC,OAAOmC,cAAcX,MAAM;YAChDW,gBAAgBI;YAChBD,YAAYzB;QACd;QAEA6B;YACE,IAAIL,cAAc;gBAChB,OAAOA;YACT;QACF;IACF;AACF;AAEO,SAAStD,yBACd4D,qBAA4C;IAE5C,OAAO,IAAIhC,gBAAgB;QACzB,MAAMO,WAAUlB,KAAK,EAAEa,UAAU;YAC/B,MAAM+B,oBAAoBd,IAAAA,wBAAU,EAAC,MAAMa;YAC3C9B,WAAWI,OAAO,CAAC2B;YACnB/B,WAAWI,OAAO,CAACjB;QACrB;IACF;AACF;AAEO,SAAShB,0BAA0B,EACxC6D,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,wBAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IACf,MAAM5D,cAAc,IAAIC;IAExB,OAAO,IAAIe,gBAAgB;QACzB,MAAMO,WAAUlB,KAAK,EAAEa,UAAU;YAC/B,4DAA4D;YAC5D,IAAI0C,UAAU;gBACZ1C,WAAWI,OAAO,CAACjB;gBACnB;YACF;YAEA,MAAMwD,YAAY,MAAMH;YACxB,IAAIC,UAAU;gBACZzC,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAAC0B;gBAC9B3C,WAAWI,OAAO,CAACjB;gBACnBuD,WAAW;YACb,OAAO;gBACL,MAAME,UAAUxD,IAAAA,wBAAU,EAACD,OAAOL;gBAClC,MAAM+D,QAAQD,QAAQE,OAAO,CAAC;gBAC9B,IAAID,UAAU,CAAC,GAAG;oBAChB,MAAME,sBACJH,QAAQI,KAAK,CAAC,GAAGH,SAASF,YAAYC,QAAQI,KAAK,CAACH;oBACtD7C,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAAC8B;oBAC9BL,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACbzC,WAAWI,OAAO,CAACjB;YACrB,OAAO;gBACLb,UAAU;oBACRoE,WAAW;gBACb;YACF;QACF;QACA,MAAMb,OAAM7B,UAAU;YACpB,gEAAgE;YAChE,MAAM2C,YAAY,MAAMH;YACxB,IAAIG,WAAW;gBACb3C,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAAC0B;YAChC;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASM,2BACPC,MAAc;IAEd,IAAIC,gBAAgB;IACpB,IAAIC,kBAAwC;IAE5C,OAAO,IAAItD,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzBA,WAAWI,OAAO,CAACjB;YACnB,IAAI,CAACgE,iBAAiBD,OAAOvC,MAAM,EAAE;gBACnCwC,gBAAgB;gBAChBC,kBAAkB,IAAI5C,QAAQ,CAAC6C;oBAC7B,wBAAwB;oBACxB,mEAAmE;oBACnE,iEAAiE;oBACjE/E,UAAU;wBACR0B,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAACiC;wBAC9BG;oBACF;gBACF;YACF;QACF;QACAxB,OAAM7B,UAAU;YACd,IAAIoD,iBAAiB,OAAOA;YAC5B,IAAI,CAACD,iBAAiBD,OAAOvC,MAAM,EAAE;gBACnCwC,gBAAgB;gBAChBnD,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAACiC;YAChC;QACF;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASI,4BACPC,UAAsC;IAEtC,IAAIC,qBAA2C;IAC/C,OAAO,IAAI1D,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzBA,WAAWI,OAAO,CAACjB;YAEnB,IAAI,CAACqE,oBAAoB;gBACvB,MAAMC,mBAAmBF,WAAW3D,SAAS;gBAE7C,wBAAwB;gBACxB,gEAAgE;gBAChE,qEAAqE;gBACrE,uEAAuE;gBACvE,8DAA8D;gBAC9D,aAAa;gBACb4D,qBAAqB,IAAIhD,QAAQ,CAAC6C,MAChC,uEAAuE;oBACvE,mEAAmE;oBACnE,wCAAwC;oBACxC,uEAAuE;oBACvE,mEAAmE;oBACnE,WAAW;oBACX1E,WAAW;wBACT,IAAI;4BACF,MAAO,KAAM;gCACX,MAAM,EAAEsB,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMuD,iBAAiBtD,IAAI;gCACnD,IAAIF,MAAM;oCACR,OAAOoD;gCACT;gCACArD,WAAWI,OAAO,CAACF;4BACrB;wBACF,EAAE,OAAOwD,KAAK;4BACZ1D,WAAW2D,KAAK,CAACD;wBACnB;wBACAL;oBACF,GAAG;YAEP;QACF;QACAxB;YACE,IAAI2B,oBAAoB;gBACtB,OAAOA;YACT;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAASI,uBACPV,MAAc;IAEd,IAAIW,cAAc;IAClB,MAAM/E,cAAc,IAAIC;IAExB,8DAA8D;IAC9D,OAAO,IAAIe,gBAAgB;QACzBO,WAAUlB,KAAK,EAAEa,UAAU;YACzB,IAAI,CAACkD,UAAUW,aAAa;gBAC1B,OAAO7D,WAAWI,OAAO,CAACjB;YAC5B;YAEA,MAAMyD,UAAUxD,IAAAA,wBAAU,EAACD,OAAOL;YAClC,IAAI8D,QAAQkB,QAAQ,CAACZ,SAAS;gBAC5BW,cAAc;gBACd,MAAME,uBAAuBnB,QAAQI,KAAK,CAAC,GAAG,CAACE,OAAOvC,MAAM;gBAC5DX,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAAC8C;YAChC,OAAO;gBACL/D,WAAWI,OAAO,CAACjB;YACrB;QACF;QACA0C,OAAM7B,UAAU;YACd,IAAIkD,QAAQ;gBACVlD,WAAWI,OAAO,CAACa,IAAAA,wBAAU,EAACiC;YAChC;QACF;IACF;AACF;AAEO,SAAS9E,gCACd4F,cAAc,EAAE,EAChBC,OAAgC;IAEhC,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAChB,MAAMrF,cAAc,IAAIC;IAExB,OAAO,IAAIe,gBAAgB;QACzB,MAAMO,WAAUlB,KAAK,EAAEa,UAAU;YAC/B,IAAI,CAACkE,aAAa,CAACC,WAAW;gBAC5B,MAAMvB,UAAUxD,IAAAA,wBAAU,EAACD,OAAOL;gBAClC,IAAI,CAACoF,aAAatB,QAAQwB,QAAQ,CAAC,UAAU;oBAC3CF,YAAY;gBACd;gBACA,IAAI,CAACC,aAAavB,QAAQwB,QAAQ,CAAC,UAAU;oBAC3CD,YAAY;gBACd;YACF;YACAnE,WAAWI,OAAO,CAACjB;QACrB;QACA0C,OAAM7B,UAAU;YACd,uEAAuE;YACvE,cAAc;YACd,IAAI,CAACkE,aAAa,CAACC,WAAW;gBAC5B,MAAME,cAAc;oBAClBH,YAAY,OAAO;oBACnBC,YAAY,OAAO;iBACpB,CAACG,MAAM,CAACC,wBAAW;gBAEpBvE,WAAWI,OAAO,CAChBa,IAAAA,wBAAU,EACR,CAAC,mDAAmD,EAAEuD,KAAKC,SAAS,CAClE;oBAAEJ;oBAAaL,aAAaA,eAAe;oBAAIU,MAAMT;gBAAU,GAC/D,SAAS,CAAC;YAGlB;QACF;IACF;AACF;AAEO,eAAe5F,mBACpBsG,YAAiC,EACjC,EACEzB,MAAM,EACN0B,iBAAiB,EACjBC,kBAAkB,EAClB/C,qBAAqB,EACrBgD,wBAAwB,EACxBC,kBAAkB,EAYnB;IAED,MAAMC,WAAW;IAEjB,6EAA6E;IAC7E,MAAMC,iBAAiB/B,SAASA,OAAOgC,KAAK,CAACF,SAAS,CAAC,EAAE,GAAG;IAE5D,IAAIH,oBAAoB;QACtB,MAAMF,aAAaQ,QAAQ;IAC7B;IAEA,MAAMC,aAA6D;QACjE,qDAAqD;QACrDnH;QAEA,gCAAgC;QAChC6D,yBAAyB,CAACgD,2BACtB5G,yBAAyB4D,yBACzB;QAEJ,wBAAwB;QACxBmD,kBAAkB,OAAOhC,2BAA2BgC,kBAAkB;QAEtE,+EAA+E;QAC/EL,oBAAoBtB,4BAA4BsB,qBAAqB;QAErE,kDAAkD;QAClDhB,uBAAuBoB;QAEvB,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ElD,yBAAyBgD,2BACrBvC,mCAAmCT,yBACnC;QAEJiD,qBACI3G,gCACE2G,mBAAmBf,WAAW,EAC9Be,mBAAmBd,OAAO,IAE5B;KACL,CAACK,MAAM,CAACC,wBAAW;IAEpB,OAAOa,WAAWC,MAAM,CACtB,CAAC1F,UAAUU,YAAcV,SAAS2F,WAAW,CAACjF,YAC9CsE;AAEJ"}