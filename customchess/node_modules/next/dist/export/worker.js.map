{"version": 3, "sources": ["../../src/export/worker.ts"], "names": ["exportPage", "process", "env", "NEXT_IS_EXPORT_WORKER", "envConfig", "require", "globalThis", "__NEXT_DATA__", "nextExport", "exportPageImpl", "input", "fileWriter", "path", "pathMap", "distDir", "pagesDataDir", "buildExport", "serverRuntimeConfig", "subFolders", "optimizeFonts", "optimizeCss", "disableOptimizedLoading", "debugOutput", "isrMemoryCacheSize", "fetchCache", "fetchCacheKeyPrefix", "incremental<PERSON>ache<PERSON>andlerPath", "enableExperimentalReact", "ampValidator<PERSON>ath", "trailingSlash", "renderOpts", "deploymentId", "NEXT_DEPLOYMENT_ID", "__NEXT_EXPERIMENTAL_REACT", "page", "_isAppDir", "isAppDir", "_isAppPrefetch", "isAppPrefetch", "_isDynamicError", "isDynamicError", "query", "originalQuery", "req", "pathname", "normalizeAppPath", "isDynamic", "isDynamicRoute", "outDir", "join", "params", "filePath", "normalizePagePath", "ampPath", "renderAmpPath", "updatedPath", "__nextSsgPath", "locale", "__next<PERSON><PERSON><PERSON>", "localePathResult", "normalizeLocalePath", "locales", "detectedLocale", "defaultLocale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "nonLocalizedPath", "normalizedPage", "getParams", "res", "createRequestResponseMocks", "url", "statusCode", "some", "p", "endsWith", "domainLocales", "dl", "includes", "addRequestMeta", "setConfig", "publicRuntimeConfig", "runtimeConfig", "getHtmlFilename", "sep", "htmlFilename", "pageExt", "extname", "pathExt", "isBuiltinPaths", "isHtmlExtPath", "baseDir", "dirname", "htmlFilepath", "fs", "mkdir", "recursive", "incrementalCache", "createIncrementalCache", "undefined", "isAppRouteRoute", "exportAppRoute", "components", "loadComponents", "isAppPath", "fontManifest", "requireFontManifest", "supportsDynamicHTML", "originalPathname", "hasNextSupport", "isRevalidate", "exportAppPage", "exportPages", "err", "console", "error", "isError", "stack", "setHttpClientAndAgentOptions", "httpAgentOptions", "files", "baseFileWriter", "type", "content", "encodingOptions", "writeFile", "push", "exportPageSpan", "trace", "parentSpanId", "start", "Date", "now", "result", "traceAsyncFn", "duration", "ampValidations", "revalidate", "metadata", "ssgNotFound"], "mappings": ";;;;+BAiUA;;;eAA8BA;;;QAvTvB;QACA;QACA;QACA;sBAIqC;iEAC7B;gCACgB;2BACA;mCACG;yBACE;qCACA;uBACd;mCACuB;gEACzB;6BACW;0BACE;6BAEU;iCACX;wBACD;0BACA;yBACD;uBACF;2BACF;wCACa;;;;;;AAtBvCC,QAAQC,GAAG,CAACC,qBAAqB,GAAG;AAwBpC,MAAMC,YAAYC,QAAQ;AAExBC,WAAmBC,aAAa,GAAG;IACnCC,YAAY;AACd;AAEA,eAAeC,eACbC,KAAsB,EACtBC,UAAsB;IAEtB,MAAM,EACJC,IAAI,EACJC,OAAO,EACPC,OAAO,EACPC,YAAY,EACZC,cAAc,KAAK,EACnBC,mBAAmB,EACnBC,aAAa,KAAK,EAClBC,aAAa,EACbC,WAAW,EACXC,uBAAuB,EACvBC,cAAc,KAAK,EACnBC,kBAAkB,EAClBC,UAAU,EACVC,mBAAmB,EACnBC,2BAA2B,EAC3BC,uBAAuB,EACvBC,gBAAgB,EAChBC,aAAa,EACd,GAAGnB;IAEJ,IAAIA,MAAMoB,UAAU,CAACC,YAAY,EAAE;QACjC9B,QAAQC,GAAG,CAAC8B,kBAAkB,GAAGtB,MAAMoB,UAAU,CAACC,YAAY;IAChE;IACA,IAAIJ,yBAAyB;QAC3B1B,QAAQC,GAAG,CAAC+B,yBAAyB,GAAG;IAC1C;IAEA,MAAM,EACJC,IAAI,EAEJ,mCAAmC;IACnCC,WAAWC,WAAW,KAAK,EAE3B,6CAA6C;IAC7CC,gBAAgBC,gBAAgB,KAAK,EAErC,6DAA6D;IAC7DC,iBAAiBC,iBAAiB,KAAK,EAEvC,+BAA+B;IAC/BC,OAAOC,gBAAgB,CAAC,CAAC,EAC1B,GAAG7B;IAEJ,IAAI;YAwEoB8B;QAvEtB,IAAIF,QAAQ;YAAE,GAAGC,aAAa;QAAC;QAC/B,MAAME,WAAWC,IAAAA,0BAAgB,EAACX;QAClC,MAAMY,YAAYC,IAAAA,yBAAc,EAACb;QACjC,MAAMc,SAASZ,WAAWa,IAAAA,UAAI,EAACnC,SAAS,gBAAgBJ,MAAMsC,MAAM;QAEpE,IAAIE;QAEJ,MAAMC,WAAWC,IAAAA,oCAAiB,EAACxC;QACnC,MAAMyC,UAAU,CAAC,EAAEF,SAAS,IAAI,CAAC;QACjC,IAAIG,gBAAgBD;QAEpB,IAAIE,cAAcd,MAAMe,aAAa,IAAI5C;QACzC,OAAO6B,MAAMe,aAAa;QAE1B,IAAIC,SAAShB,MAAMiB,YAAY,IAAIhD,MAAMoB,UAAU,CAAC2B,MAAM;QAC1D,OAAOhB,MAAMiB,YAAY;QAEzB,IAAIhD,MAAMoB,UAAU,CAAC2B,MAAM,EAAE;YAC3B,MAAME,mBAAmBC,IAAAA,wCAAmB,EAC1ChD,MACAF,MAAMoB,UAAU,CAAC+B,OAAO;YAG1B,IAAIF,iBAAiBG,cAAc,EAAE;gBACnCP,cAAcI,iBAAiBf,QAAQ;gBACvCa,SAASE,iBAAiBG,cAAc;gBAExC,IAAIL,WAAW/C,MAAMoB,UAAU,CAACiC,aAAa,EAAE;oBAC7CT,gBAAgB,CAAC,EAAEF,IAAAA,oCAAiB,EAACG,aAAa,IAAI,CAAC;gBACzD;YACF;QACF;QAEA,gEAAgE;QAChE,0DAA0D;QAC1D,MAAMS,qBAAqBC,OAAOC,IAAI,CAACxB,eAAeyB,MAAM,GAAG;QAE/D,iDAAiD;QACjD,MAAM,EAAEvB,UAAUwB,gBAAgB,EAAE,GAAGR,IAAAA,wCAAmB,EACxDhD,MACAF,MAAMoB,UAAU,CAAC+B,OAAO;QAG1B,IAAIf,aAAaZ,SAASkC,kBAAkB;YAC1C,MAAMC,iBAAiBjC,WAAWS,IAAAA,0BAAgB,EAACX,QAAQA;YAE3DgB,SAASoB,IAAAA,oBAAS,EAACD,gBAAgBd;YACnC,IAAIL,QAAQ;gBACVT,QAAQ;oBACN,GAAGA,KAAK;oBACR,GAAGS,MAAM;gBACX;YACF;QACF;QAEA,MAAM,EAAEP,GAAG,EAAE4B,GAAG,EAAE,GAAGC,IAAAA,uCAA0B,EAAC;YAAEC,KAAKlB;QAAY;QAEnE,6DAA6D;QAC7D,KAAK,MAAMmB,cAAc;YAAC;YAAK;SAAI,CAAE;YACnC,IACE;gBACE,CAAC,CAAC,EAAEA,WAAW,CAAC;gBAChB,CAAC,CAAC,EAAEA,WAAW,KAAK,CAAC;gBACrB,CAAC,CAAC,EAAEA,WAAW,WAAW,CAAC;aAC5B,CAACC,IAAI,CAAC,CAACC,IAAMA,MAAMrB,eAAe,CAAC,CAAC,EAAEE,OAAO,EAAEmB,EAAE,CAAC,KAAKrB,cACxD;gBACAgB,IAAIG,UAAU,GAAGA;YACnB;QACF;QAEA,+DAA+D;QAC/D,IAAI7C,iBAAiB,GAACc,WAAAA,IAAI8B,GAAG,qBAAP9B,SAASkC,QAAQ,CAAC,OAAM;YAC5ClC,IAAI8B,GAAG,IAAI;QACb;QAEA,IACEhB,UACAzC,eACAN,MAAMoB,UAAU,CAACgD,aAAa,IAC9BpE,MAAMoB,UAAU,CAACgD,aAAa,CAACH,IAAI,CACjC,CAACI;gBACgCA;mBAA/BA,GAAGhB,aAAa,KAAKN,YAAUsB,cAAAA,GAAGlB,OAAO,qBAAVkB,YAAYC,QAAQ,CAACvB,UAAU;YAElE;YACAwB,IAAAA,2BAAc,EAACtC,KAAK,wBAAwB;QAC9C;QAEAvC,UAAU8E,SAAS,CAAC;YAClBjE;YACAkE,qBAAqBzE,MAAMoB,UAAU,CAACsD,aAAa;QACrD;QAEA,MAAMC,kBAAkB,CAACT,IACvB1D,aAAa,CAAC,EAAE0D,EAAE,EAAEU,SAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAEV,EAAE,KAAK,CAAC;QAEnD,IAAIW,eAAeF,gBAAgBlC;QAEnC,gFAAgF;QAChF,wBAAwB;QACxB,MAAMqC,UAAU1C,aAAaV,WAAW,KAAKqD,IAAAA,aAAO,EAACvD;QACrD,MAAMwD,UAAU5C,aAAaV,WAAW,KAAKqD,IAAAA,aAAO,EAAC7E;QAErD,6CAA6C;QAC7C,IAAIA,SAAS,aAAa;YACxB2E,eAAe3E;QACjB,OAEK,IAAI4E,YAAYE,WAAWA,YAAY,IAAI;YAC9C,MAAMC,iBAAiB;gBAAC;gBAAQ;aAAO,CAAChB,IAAI,CAC1C,CAACC,IAAMA,MAAMhE,QAAQgE,MAAMhE,OAAO;YAEpC,mFAAmF;YACnF,8CAA8C;YAC9C,MAAMgF,gBAAgB,CAACD,kBAAkB/E,KAAKiE,QAAQ,CAAC;YACvDU,eAAeK,gBAAgBP,gBAAgBzE,QAAQA;QACzD,OAAO,IAAIA,SAAS,KAAK;YACvB,+CAA+C;YAC/C2E,eAAe;QACjB;QAEA,MAAMM,UAAU5C,IAAAA,UAAI,EAACD,QAAQ8C,IAAAA,aAAO,EAACP;QACrC,IAAIQ,eAAe9C,IAAAA,UAAI,EAACD,QAAQuC;QAEhC,MAAMS,iBAAE,CAACC,KAAK,CAACJ,SAAS;YAAEK,WAAW;QAAK;QAE1C,mEAAmE;QACnE,gCAAgC;QAChC,MAAMC,mBACJ/D,YAAYZ,aACR4E,IAAAA,8CAAsB,EACpB1E,6BACAH,oBACAE,qBACAX,WAEFuF;QAEN,qBAAqB;QACrB,IAAIjE,YAAYkE,IAAAA,gCAAe,EAACpE,OAAO;YACrC,OAAO,MAAMqE,IAAAA,wBAAc,EACzB5D,KACA4B,KACArB,QACAhB,MACAiE,kBACArF,SACAiF,cACApF;QAEJ;QAEA,MAAM6F,aAAa,MAAMC,IAAAA,8BAAc,EAAC;YACtC3F;YACAoB;YACAwE,WAAWtE;QACb;QAEA,MAAMN,aAA+B;YACnC,GAAG0E,UAAU;YACb,GAAG9F,MAAMoB,UAAU;YACnBuB,SAASC;YACTJ;YACA/B;YACAC;YACAC;YACAsF,cAAcxF,gBAAgByF,IAAAA,4BAAmB,EAAC9F,WAAW;YAC7D2C;YACAoD,qBAAqB;YACrBC,kBAAkB5E;QACpB;QAEA,IAAI6E,sBAAc,EAAE;YAClBjF,WAAWkF,YAAY,GAAG;QAC5B;QAEA,mBAAmB;QACnB,IAAI5E,UAAU;YACZ,qEAAqE;YACrE,cAAc;YACdN,WAAWqE,gBAAgB,GAAGA;YAE9B,OAAO,MAAMc,IAAAA,sBAAa,EACxBtE,KACA4B,KACArC,MACAtB,MACAgC,UACAH,OACAX,YACAiE,cACAzE,aACAkB,gBACAF,eACA3B;QAEJ;QAEA,OAAO,MAAMuG,IAAAA,kBAAW,EACtBvE,KACA4B,KACA3D,MACAsB,MACAO,OACAsD,cACAR,cACAlC,SACAnC,YACA8B,QACApB,kBACAb,cACAC,aACA8B,WACAkB,oBACAlC,YACA0E,YACA7F;IAEJ,EAAE,OAAOwG,KAAK;QACZC,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAEzG,KAAK,gEAAgE,CAAC,GAC1G0G,CAAAA,IAAAA,gBAAO,EAACH,QAAQA,IAAII,KAAK,GAAGJ,IAAII,KAAK,GAAGJ,GAAE;QAG/C,OAAO;YAAEE,OAAO;QAAK;IACvB;AACF;AAEe,eAAerH,WAC5BU,KAAsB;IAEtB,4BAA4B;IAC5B8G,IAAAA,+CAA4B,EAAC;QAC3BC,kBAAkB/G,MAAM+G,gBAAgB;IAC1C;IAEA,MAAMC,QAA4B,EAAE;IACpC,MAAMC,iBAA6B,OACjCC,MACAhH,MACAiH,SACAC,kBAAkB,OAAO;QAEzB,MAAM9B,iBAAE,CAACC,KAAK,CAACH,IAAAA,aAAO,EAAClF,OAAO;YAAEsF,WAAW;QAAK;QAChD,MAAMF,iBAAE,CAAC+B,SAAS,CAACnH,MAAMiH,SAASC;QAClCJ,MAAMM,IAAI,CAAC;YAAEJ;YAAMhH;QAAK;IAC1B;IAEA,MAAMqH,iBAAiBC,IAAAA,YAAK,EAAC,sBAAsBxH,MAAMyH,YAAY;IAErE,MAAMC,QAAQC,KAAKC,GAAG;IAEtB,mBAAmB;IACnB,MAAMC,SAAS,MAAMN,eAAeO,YAAY,CAAC;QAC/C,OAAO,MAAM/H,eAAeC,OAAOiH;IACrC;IAEA,kDAAkD;IAClD,IAAI,CAACY,QAAQ;IAEb,iDAAiD;IACjD,IAAI,WAAWA,QAAQ;QACrB,OAAO;YAAElB,OAAOkB,OAAOlB,KAAK;YAAEoB,UAAUJ,KAAKC,GAAG,KAAKF;YAAOV,OAAO,EAAE;QAAC;IACxE;IAEA,sCAAsC;IACtC,OAAO;QACLe,UAAUJ,KAAKC,GAAG,KAAKF;QACvBV;QACAgB,gBAAgBH,OAAOG,cAAc;QACrCC,YAAYJ,OAAOI,UAAU;QAC7BC,UAAUL,OAAOK,QAAQ;QACzBC,aAAaN,OAAOM,WAAW;IACjC;AACF"}