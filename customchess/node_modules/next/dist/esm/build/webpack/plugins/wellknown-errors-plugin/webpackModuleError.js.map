{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts"], "names": ["readFileSync", "path", "getBabelError", "getCssError", "getScssError", "getNotFoundError", "getImageError", "isError", "getRscError", "getNextFontError", "getNextAppLoaderError", "getNextInvalidImportError", "getFileData", "compilation", "m", "resolved", "ctx", "compiler", "context", "resource", "res", "relative", "replace", "posix", "sep", "startsWith", "requestShortener", "readableIdentifier", "request", "userRequest", "content", "resolve", "getModuleBuildError", "input", "name", "Boolean", "module", "error", "err", "sourceFilename", "sourceContent", "notFoundError", "imageError", "babel", "css", "scss", "rsc", "nextFont", "nextApp<PERSON><PERSON>der", "invalidImportError"], "mappings": "AAAA,SAASA,YAAY,QAAQ,KAAI;AACjC,YAAYC,UAAU,OAAM;AAG5B,SAASC,aAAa,QAAQ,eAAc;AAC5C,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,YAAY,QAAQ,cAAa;AAC1C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,uBAAsB;AAEtE,OAAOC,aAAa,2BAA0B;AAC9C,SAASC,WAAW,QAAQ,aAAY;AACxC,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,qBAAqB,QAAQ,4BAA2B;AACjE,SAASC,yBAAyB,QAAQ,gCAA+B;AAEzE,SAASC,YACPC,WAAgC,EAChCC,CAAM;QAGmBD;IADzB,IAAIE;IACJ,IAAIC,MAAqBH,EAAAA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,OAAO,KAAI;IAC1D,IAAIF,QAAQ,QAAQ,OAAOF,EAAEK,QAAQ,KAAK,UAAU;QAClD,MAAMC,MAAMnB,KAAKoB,QAAQ,CAACL,KAAKF,EAAEK,QAAQ,EAAEG,OAAO,CAAC,OAAOrB,KAAKsB,KAAK,CAACC,GAAG;QACxET,WAAWK,IAAIK,UAAU,CAAC,OAAOL,MAAM,CAAC,CAAC,EAAEnB,KAAKsB,KAAK,CAACC,GAAG,CAAC,EAAEJ,IAAI,CAAC;IACnE,OAAO;QACL,MAAMM,mBAAmBb,YAAYa,gBAAgB;QACrD,IAAI,QAAOZ,qBAAAA,EAAGa,kBAAkB,MAAK,YAAY;YAC/CZ,WAAWD,EAAEa,kBAAkB,CAACD;QAClC,OAAO;YACLX,WAAWD,EAAEc,OAAO,IAAId,EAAEe,WAAW;QACvC;IACF;IAEA,IAAId,UAAU;QACZ,IAAIe,UAAyB;QAC7B,IAAI;YACFA,UAAU9B,aACRgB,MAAMf,KAAK8B,OAAO,CAACf,KAAKD,YAAYA,UACpC;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;YAACA;YAAUe;SAAQ;IAC5B;IAEA,OAAO;QAAC;QAAa;KAAK;AAC5B;AAEA,OAAO,eAAeE,oBACpBf,QAA0B,EAC1BJ,WAAgC,EAChCoB,KAAU;IAEV,IACE,CACE,CAAA,OAAOA,UAAU,YAChBA,CAAAA,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,sBACfD,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,qBAAoB,KACtCC,QAAQF,MAAMG,MAAM,KACpB7B,QAAQ0B,MAAMI,KAAK,CAAA,GAErB;QACA,OAAO;IACT;IAEA,MAAMC,MAAaL,MAAMI,KAAK;IAC9B,MAAM,CAACE,gBAAgBC,cAAc,GAAG5B,YAAYC,aAAaoB,MAAMG,MAAM;IAE7E,MAAMK,gBAAgB,MAAMpC,iBAC1BQ,aACAoB,OACAM,gBACAN,MAAMG,MAAM;IAEd,IAAIK,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAMC,aAAa,MAAMpC,cAAcO,aAAaoB,OAAOK;IAC3D,IAAII,eAAe,OAAO;QACxB,OAAOA;IACT;IAEA,MAAMC,QAAQzC,cAAcqC,gBAAgBD;IAC5C,IAAIK,UAAU,OAAO;QACnB,OAAOA;IACT;IAEA,MAAMC,MAAMzC,YAAYoC,gBAAgBD;IACxC,IAAIM,QAAQ,OAAO;QACjB,OAAOA;IACT;IAEA,MAAMC,OAAOzC,aAAamC,gBAAgBC,eAAeF;IACzD,IAAIO,SAAS,OAAO;QAClB,OAAOA;IACT;IAEA,MAAMC,MAAMtC,YACV+B,gBACAD,KACAL,MAAMG,MAAM,EACZvB,aACAI;IAEF,IAAI6B,QAAQ,OAAO;QACjB,OAAOA;IACT;IAEA,MAAMC,WAAWtC,iBAAiB6B,KAAKL,MAAMG,MAAM;IACnD,IAAIW,aAAa,OAAO;QACtB,OAAOA;IACT;IAEA,MAAMC,gBAAgBtC,sBAAsB4B,KAAKL,MAAMG,MAAM,EAAEnB;IAC/D,IAAI+B,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAMC,qBAAqBtC,0BACzB2B,KACAL,MAAMG,MAAM,EACZvB,aACAI;IAEF,IAAIgC,uBAAuB,OAAO;QAChC,OAAOA;IACT;IAEA,OAAO;AACT"}