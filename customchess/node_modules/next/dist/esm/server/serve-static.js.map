{"version": 3, "sources": ["../../src/server/serve-static.ts"], "names": ["send", "mime", "define", "serveStatic", "req", "res", "path", "opts", "Promise", "resolve", "reject", "on", "err", "Error", "code", "pipe", "getContentType", "extWithoutDot", "getType", "lookup", "getExtension", "contentType", "extension"], "mappings": "AACA,OAAOA,UAAU,0BAAyB;AAE1C,gGAAgG;AAChG,0FAA0F;AAC1FA,KAAKC,IAAI,CAACC,MAAM,CAAC;IACf,cAAc;QAAC;KAAO;AACxB;AAEA,OAAO,SAASC,YACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAY,EACZC,IAAiC;IAEjC,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BV,KAAKI,KAAKE,MAAMC,MACbI,EAAE,CAAC,aAAa;YACf,yCAAyC;YACzC,MAAMC,MAAW,IAAIC,MAAM;YAC3BD,IAAIE,IAAI,GAAG;YACXJ,OAAOE;QACT,GACCD,EAAE,CAAC,SAASD,QACZK,IAAI,CAACV,KACLM,EAAE,CAAC,UAAUF;IAClB;AACF;AAEA,OAAO,SAASO,eAAeC,aAAqB;IAClD,MAAM,EAAEhB,IAAI,EAAE,GAAGD;IACjB,IAAI,aAAaC,MAAM;QACrB,MAAM;QACN,OAAOA,KAAKiB,OAAO,CAACD;IACtB;IACA,MAAM;IACN,OAAO,AAAChB,KAAakB,MAAM,CAACF;AAC9B;AAEA,OAAO,SAASG,aAAaC,WAAmB;IAC9C,MAAM,EAAEpB,IAAI,EAAE,GAAGD;IACjB,IAAI,kBAAkBC,MAAM;QAC1B,MAAM;QACN,OAAOA,KAAKmB,YAAY,CAACC;IAC3B;IACA,MAAM;IACN,OAAO,AAACpB,KAAaqB,SAAS,CAACD;AACjC"}