{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "names": ["useEffect", "hydrationErrorWarning", "hydrationErrorComponentStack", "isNextRouterError", "RuntimeError<PERSON>andler", "hadRuntimeError", "isHydrationError", "error", "message", "match", "window", "Error", "stackTraceLimit", "errorQueue", "rejectionQueue", "errorHandlers", "rejectionHandlers", "addEventListener", "ev", "preventDefault", "stack", "includes", "_componentStack", "e", "push", "handler", "reason", "useErrorHandler", "handleOnUnhandledError", "handleOnUnhandledRejection", "for<PERSON>ach", "splice", "indexOf"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SACEC,qBAAqB,EACrBC,4BAA4B,QACvB,yBAAwB;AAC/B,SAASC,iBAAiB,QAAQ,gCAA+B;AAIjE,OAAO,MAAMC,sBAAsB;IACjCC,iBAAiB;AACnB,EAAC;AAED,SAASC,iBAAiBC,KAAY;IACpC,OACEA,MAAMC,OAAO,CAACC,KAAK,CAAC,wDACpB;AAEJ;AAEA,IAAI,OAAOC,WAAW,aAAa;IACjC,IAAI;QACF,oDAAoD;QACpDC,MAAMC,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;AACX;AAEA,MAAMC,aAA2B,EAAE;AACnC,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,oBAAyC,EAAE;AAEjD,IAAI,OAAON,WAAW,aAAa;IACjC,6EAA6E;IAC7E,0EAA0E;IAC1E,yBAAyB;IACzBA,OAAOO,gBAAgB,CAAC,SAAS,CAACC;QAChC,IAAIf,kBAAkBe,GAAGX,KAAK,GAAG;YAC/BW,GAAGC,cAAc;YACjB;QACF;QAEA,MAAMZ,QAAQW,sBAAAA,GAAIX,KAAK;QACvB,IACE,CAACA,SACD,CAAEA,CAAAA,iBAAiBI,KAAI,KACvB,OAAOJ,MAAMa,KAAK,KAAK,UACvB;YACA,8DAA8D;YAC9D;QACF;QAEA,IACEd,iBAAiBC,UACjB,CAACA,MAAMC,OAAO,CAACa,QAAQ,CACrB,2DAEF;YACA,IAAIpB,uBAAuB;gBACzB,mEAAmE;gBACnE,iDAAiD;gBACjDM,MAAMC,OAAO,IAAI,SAASP;YAC5B;YACA,IAAIC,8BAA8B;gBAE9BK,MAAce,eAAe,GAAGpB;YACpC;YACAK,MAAMC,OAAO,IACX;QACJ;QAEA,MAAMe,IAAIhB;QACVM,WAAWW,IAAI,CAACD;QAChB,KAAK,MAAME,WAAWV,cAAe;YACnCU,QAAQF;QACV;IACF;IACAb,OAAOO,gBAAgB,CACrB,sBACA,CAACC;QACC,MAAMQ,SAASR,sBAAAA,GAAIQ,MAAM;QACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBf,KAAI,KACxB,OAAOe,OAAON,KAAK,KAAK,UACxB;YACA,8DAA8D;YAC9D;QACF;QAEA,MAAMG,IAAIG;QACVZ,eAAeU,IAAI,CAACD;QACpB,KAAK,MAAME,WAAWT,kBAAmB;YACvCS,QAAQF;QACV;IACF;AAEJ;AAEA,OAAO,SAASI,gBACdC,sBAAoC,EACpCC,0BAAwC;IAExC7B,UAAU;QACR,wBAAwB;QACxBa,WAAWiB,OAAO,CAACF;QACnBd,eAAegB,OAAO,CAACD;QAEvB,wBAAwB;QACxBd,cAAcS,IAAI,CAACI;QACnBZ,kBAAkBQ,IAAI,CAACK;QAEvB,OAAO;YACL,oBAAoB;YACpBd,cAAcgB,MAAM,CAAChB,cAAciB,OAAO,CAACJ,yBAAyB;YACpEZ,kBAAkBe,MAAM,CACtBf,kBAAkBgB,OAAO,CAACH,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD"}