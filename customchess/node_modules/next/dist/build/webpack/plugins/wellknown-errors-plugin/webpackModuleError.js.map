{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts"], "names": ["getModuleBuildError", "getFileData", "compilation", "m", "resolved", "ctx", "compiler", "context", "resource", "res", "path", "relative", "replace", "posix", "sep", "startsWith", "requestShortener", "readableIdentifier", "request", "userRequest", "content", "readFileSync", "resolve", "input", "name", "Boolean", "module", "isError", "error", "err", "sourceFilename", "sourceContent", "notFoundError", "getNotFoundError", "imageError", "getImageError", "babel", "getBabelError", "css", "getCssError", "scss", "getScssError", "rsc", "getRscError", "nextFont", "getNextFontError", "nextApp<PERSON><PERSON>der", "getNextAppLoaderError", "invalidImportError", "getNextInvalidImportError"], "mappings": ";;;;+BA+CsBA;;;eAAAA;;;oBA/CO;8DACP;4BAGQ;0BACF;2BACC;oCACmB;gEAE5B;0BACQ;oCACK;yCACK;6CACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,SAASC,YACPC,WAAgC,EAChCC,CAAM;QAGmBD;IADzB,IAAIE;IACJ,IAAIC,MAAqBH,EAAAA,wBAAAA,YAAYI,QAAQ,qBAApBJ,sBAAsBK,OAAO,KAAI;IAC1D,IAAIF,QAAQ,QAAQ,OAAOF,EAAEK,QAAQ,KAAK,UAAU;QAClD,MAAMC,MAAMC,MAAKC,QAAQ,CAACN,KAAKF,EAAEK,QAAQ,EAAEI,OAAO,CAAC,OAAOF,MAAKG,KAAK,CAACC,GAAG;QACxEV,WAAWK,IAAIM,UAAU,CAAC,OAAON,MAAM,CAAC,CAAC,EAAEC,MAAKG,KAAK,CAACC,GAAG,CAAC,EAAEL,IAAI,CAAC;IACnE,OAAO;QACL,MAAMO,mBAAmBd,YAAYc,gBAAgB;QACrD,IAAI,QAAOb,qBAAAA,EAAGc,kBAAkB,MAAK,YAAY;YAC/Cb,WAAWD,EAAEc,kBAAkB,CAACD;QAClC,OAAO;YACLZ,WAAWD,EAAEe,OAAO,IAAIf,EAAEgB,WAAW;QACvC;IACF;IAEA,IAAIf,UAAU;QACZ,IAAIgB,UAAyB;QAC7B,IAAI;YACFA,UAAUC,IAAAA,gBAAY,EACpBhB,MAAMK,MAAKY,OAAO,CAACjB,KAAKD,YAAYA,UACpC;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;YAACA;YAAUgB;SAAQ;IAC5B;IAEA,OAAO;QAAC;QAAa;KAAK;AAC5B;AAEO,eAAepB,oBACpBM,QAA0B,EAC1BJ,WAAgC,EAChCqB,KAAU;IAEV,IACE,CACE,CAAA,OAAOA,UAAU,YAChBA,CAAAA,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,sBACfD,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,qBAAoB,KACtCC,QAAQF,MAAMG,MAAM,KACpBC,IAAAA,gBAAO,EAACJ,MAAMK,KAAK,CAAA,GAErB;QACA,OAAO;IACT;IAEA,MAAMC,MAAaN,MAAMK,KAAK;IAC9B,MAAM,CAACE,gBAAgBC,cAAc,GAAG9B,YAAYC,aAAaqB,MAAMG,MAAM;IAE7E,MAAMM,gBAAgB,MAAMC,IAAAA,oCAAgB,EAC1C/B,aACAqB,OACAO,gBACAP,MAAMG,MAAM;IAEd,IAAIM,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAME,aAAa,MAAMC,IAAAA,iCAAa,EAACjC,aAAaqB,OAAOM;IAC3D,IAAIK,eAAe,OAAO;QACxB,OAAOA;IACT;IAEA,MAAME,QAAQC,IAAAA,yBAAa,EAACP,gBAAgBD;IAC5C,IAAIO,UAAU,OAAO;QACnB,OAAOA;IACT;IAEA,MAAME,MAAMC,IAAAA,qBAAW,EAACT,gBAAgBD;IACxC,IAAIS,QAAQ,OAAO;QACjB,OAAOA;IACT;IAEA,MAAME,OAAOC,IAAAA,uBAAY,EAACX,gBAAgBC,eAAeF;IACzD,IAAIW,SAAS,OAAO;QAClB,OAAOA;IACT;IAEA,MAAME,MAAMC,IAAAA,qBAAW,EACrBb,gBACAD,KACAN,MAAMG,MAAM,EACZxB,aACAI;IAEF,IAAIoC,QAAQ,OAAO;QACjB,OAAOA;IACT;IAEA,MAAME,WAAWC,IAAAA,oCAAgB,EAAChB,KAAKN,MAAMG,MAAM;IACnD,IAAIkB,aAAa,OAAO;QACtB,OAAOA;IACT;IAEA,MAAME,gBAAgBC,IAAAA,8CAAqB,EAAClB,KAAKN,MAAMG,MAAM,EAAEpB;IAC/D,IAAIwC,kBAAkB,OAAO;QAC3B,OAAOA;IACT;IAEA,MAAME,qBAAqBC,IAAAA,sDAAyB,EAClDpB,KACAN,MAAMG,MAAM,EACZxB,aACAI;IAEF,IAAI0C,uBAAuB,OAAO;QAChC,OAAOA;IACT;IAEA,OAAO;AACT"}