{"version": 3, "sources": ["../../../../src/build/webpack/loaders/utils.ts"], "names": ["isClientComponentEntryModule", "regexCSS", "isCSSMod", "getActions", "generateActionId", "encodeToBase64", "decodeFromBase64", "imageExtensions", "imageRegex", "RegExp", "join", "mod", "rscInfo", "buildInfo", "rsc", "hasClientDirective", "isClientRef", "isActionLayerEntry", "actions", "type", "RSC_MODULE_TYPES", "client", "test", "resource", "loaders", "some", "loader", "includes", "filePath", "exportName", "createHash", "update", "digest", "obj", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "str", "parse"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAMgBA,4BAA4B;eAA5BA;;IAaHC,QAAQ;eAARA;;IAIGC,QAAQ;eAARA;;IAiBAC,UAAU;eAAVA;;IAOAC,gBAAgB;eAAhBA;;IAMAC,cAAc;eAAdA;;IAIAC,gBAAgB;eAAhBA;;;wBAzDW;2BACM;AAEjC,MAAMC,kBAAkB;IAAC;IAAO;IAAQ;IAAO;IAAQ;IAAQ;IAAO;CAAM;AAC5E,MAAMC,aAAa,IAAIC,OAAO,CAAC,IAAI,EAAEF,gBAAgBG,IAAI,CAAC,KAAK,EAAE,CAAC;AAE3D,SAASV,6BAA6BW,GAG5C;IACC,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,MAAMC,qBAAqBH,2BAAAA,QAASI,WAAW;IAC/C,MAAMC,qBACJL,CAAAA,2BAAAA,QAASM,OAAO,KAAIN,CAAAA,2BAAAA,QAASO,IAAI,MAAKC,2BAAgB,CAACC,MAAM;IAC/D,OACEN,sBAAsBE,sBAAsBT,WAAWc,IAAI,CAACX,IAAIY,QAAQ;AAE5E;AAEO,MAAMtB,WAAW;AAIjB,SAASC,SAASS,GAIxB;QAIGA;IAHF,OAAO,CAAC,CACNA,CAAAA,IAAIQ,IAAI,KAAK,sBACZR,IAAIY,QAAQ,IAAItB,SAASqB,IAAI,CAACX,IAAIY,QAAQ,OAC3CZ,eAAAA,IAAIa,OAAO,qBAAXb,aAAac,IAAI,CACf,CAAC,EAAEC,MAAM,EAAE,GACTA,OAAOC,QAAQ,CAAC,iCAChBD,OAAOC,QAAQ,CAAC,wCAChBD,OAAOC,QAAQ,CAAC,4CACpB;AAEJ;AAEO,SAASxB,WAAWQ,GAG1B;QACQA,oBAAAA;IAAP,QAAOA,iBAAAA,IAAIE,SAAS,sBAAbF,qBAAAA,eAAeG,GAAG,qBAAlBH,mBAAoBO,OAAO;AACpC;AAEO,SAASd,iBAAiBwB,QAAgB,EAAEC,UAAkB;IACnE,OAAOC,IAAAA,kBAAU,EAAC,QACfC,MAAM,CAACH,WAAW,MAAMC,YACxBG,MAAM,CAAC;AACZ;AAEO,SAAS3B,eAA6B4B,GAAM;IACjD,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,MAAMK,QAAQ,CAAC;AACnD;AAEO,SAAShC,iBAA+BiC,GAAW;IACxD,OAAOH,KAAKI,KAAK,CAACN,OAAOC,IAAI,CAACI,KAAK,UAAUD,QAAQ,CAAC;AACxD"}