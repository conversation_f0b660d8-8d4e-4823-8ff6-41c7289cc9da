{"version": 3, "sources": ["../../../src/build/webpack/utils.ts"], "names": ["traverseModules", "forEachEntryModule", "compilation", "callback", "filterChunkGroup", "chunkGroups", "for<PERSON>ach", "chunkGroup", "chunks", "chunk", "chunkModules", "chunkGraph", "getChunkModulesIterable", "mod", "modId", "getModuleId", "anyModule", "modules", "subMod", "name", "entry", "entries", "startsWith", "isAppRouteRoute", "entryDependency", "dependencies", "request", "entryModule", "moduleGraph", "getResolvedModule", "dependency", "modRequest", "includes"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,eAAe;eAAfA;;IAiCAC,kBAAkB;eAAlBA;;;iCAnCgB;AAEzB,SAASD,gBACdE,WAAgC,EAChCC,QAKQ,EACRC,gBAA8D;IAE9DF,YAAYG,WAAW,CAACC,OAAO,CAAC,CAACC;QAC/B,IAAIH,oBAAoB,CAACA,iBAAiBG,aAAa;YACrD;QACF;QACAA,WAAWC,MAAM,CAACF,OAAO,CAAC,CAACG;YACzB,MAAMC,eAAeR,YAAYS,UAAU,CAACC,uBAAuB,CACjEH;YAGF,KAAK,MAAMI,OAAOH,aAAc;gBAC9B,MAAMI,QAAQZ,YAAYS,UAAU,CAACI,WAAW,CAACF;gBACjDV,SAASU,KAAKJ,OAAOF,YAAYO;gBACjC,MAAME,YAAYH;gBAClB,IAAIG,UAAUC,OAAO,EAAE;oBACrB,KAAK,MAAMC,UAAUF,UAAUC,OAAO,CACpCd,SAASe,QAAQT,OAAOF,YAAYO;gBACxC;YACF;QACF;IACF;AACF;AAGO,SAASb,mBACdC,WAAgB,EAChBC,QAA6E;IAE7E,KAAK,MAAM,CAACgB,MAAMC,MAAM,IAAIlB,YAAYmB,OAAO,CAACA,OAAO,GAAI;YAYvDD;QAXF,gCAAgC;QAChC,IACED,KAAKG,UAAU,CAAC,aAChB,4BAA4B;QAC3BH,KAAKG,UAAU,CAAC,WAAWC,IAAAA,gCAAe,EAACJ,OAC5C;YACA;QACF;QAEA,wDAAwD;QACxD,MAAMK,mBACJJ,sBAAAA,MAAMK,YAAY,qBAAlBL,mBAAoB,CAAC,EAAE;QACzB,mDAAmD;QACnD,IAAI,CAACI,mBAAmB,CAACA,gBAAgBE,OAAO,EAAE;QAElD,MAAMA,UAAUF,gBAAgBE,OAAO;QAEvC,IACE,CAACA,QAAQJ,UAAU,CAAC,4BACpB,CAACI,QAAQJ,UAAU,CAAC,qBAEpB;QAEF,IAAIK,cACFzB,YAAY0B,WAAW,CAACC,iBAAiB,CAACL;QAE5C,IAAIE,QAAQJ,UAAU,CAAC,0BAA0B;YAC/CK,YAAYF,YAAY,CAACnB,OAAO,CAAC,CAACwB;gBAChC,MAAMC,aAAiC,AAACD,WAAmBJ,OAAO;gBAClE,IAAIK,8BAAAA,WAAYC,QAAQ,CAAC,oBAAoB;oBAC3CL,cAAczB,YAAY0B,WAAW,CAACC,iBAAiB,CAACC;gBAC1D;YACF;QACF;QAEA3B,SAAS;YAAEgB;YAAMQ;QAAY;IAC/B;AACF"}