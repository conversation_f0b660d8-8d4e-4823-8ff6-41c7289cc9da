{"version": 3, "sources": ["../../src/build/spinner.ts"], "names": ["createSpinner", "dots<PERSON>pinner", "frames", "interval", "text", "options", "logFn", "console", "log", "spinner", "prefixText", "Log", "prefixes", "info", "suffixText", "event", "process", "stdout", "isTTY", "ora", "undefined", "stream", "start", "origLog", "origWarn", "warn", "origError", "error", "origStop", "stop", "bind", "origStopAndPersist", "stopAndPersist", "logHandle", "method", "args", "resetLog"], "mappings": ";;;;+BAQA;;;eAAwBA;;;4DARR;6DACK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,cAAc;IAClBC,QAAQ;QAAC;QAAK;QAAM;KAAM;IAC1BC,UAAU;AACZ;AAEe,SAASH,cACtBI,IAAY,EACZC,UAAuB,CAAC,CAAC,EACzBC,QAAkCC,QAAQC,GAAG;IAE7C,IAAIC;IAEJ,MAAMC,aAAa,CAAC,CAAC,EAAEC,KAAIC,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAET,KAAK,CAAC,CAAC;IACnD,uEAAuE;IACvE,MAAMU,aAAa,CAAC,GAAG,EAAEH,KAAIC,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAEX,KAAK,CAAC,CAAC;IAEtD,IAAIY,QAAQC,MAAM,CAACC,KAAK,EAAE;QACxBT,UAAUU,IAAAA,YAAG,EAAC;YACZf,MAAMgB;YACNV;YACAD,SAASR;YACToB,QAAQL,QAAQC,MAAM;YACtB,GAAGZ,OAAO;QACZ,GAAGiB,KAAK;QAER,2DAA2D;QAC3D,+DAA+D;QAC/D,MAAMC,UAAUhB,QAAQC,GAAG;QAC3B,MAAMgB,WAAWjB,QAAQkB,IAAI;QAC7B,MAAMC,YAAYnB,QAAQoB,KAAK;QAC/B,MAAMC,WAAWnB,QAAQoB,IAAI,CAACC,IAAI,CAACrB;QACnC,MAAMsB,qBAAqBtB,QAAQuB,cAAc,CAACF,IAAI,CAACrB;QAEvD,MAAMwB,YAAY,CAACC,QAAaC;YAC9BP;YACAM,UAAUC;YACV1B,QAASa,KAAK;QAChB;QAEAf,QAAQC,GAAG,GAAG,CAAC,GAAG2B,OAAcF,UAAUV,SAASY;QACnD5B,QAAQkB,IAAI,GAAG,CAAC,GAAGU,OAAcF,UAAUT,UAAUW;QACrD5B,QAAQoB,KAAK,GAAG,CAAC,GAAGQ,OAAcF,UAAUP,WAAWS;QAEvD,MAAMC,WAAW;YACf7B,QAAQC,GAAG,GAAGe;YACdhB,QAAQkB,IAAI,GAAGD;YACfjB,QAAQoB,KAAK,GAAGD;QAClB;QACAjB,QAAQoB,IAAI,GAAG;YACbD;YACAQ;YACA,OAAO3B;QACT;QACAA,QAAQuB,cAAc,GAAG;YACvB,IAAIlB,YAAY;gBACd,IAAIL,SAAS;oBACXA,QAAQL,IAAI,GAAGU;gBACjB,OAAO;oBACLR,MAAMQ;gBACR;YACF;YACAiB;YACAK;YACA,OAAO3B;QACT;IACF,OAAO,IAAIC,cAAcN,MAAM;QAC7BE,MAAMI,aAAaA,aAAa,QAAQN;IAC1C;IAEA,OAAOK;AACT"}