{"c": ["app/auth/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fanjanbharadwaj%2FDocuments%2F2025%2Fcustomchess%2Fsrc%2Fapp%2Fauth%2Fpage.tsx&server=false!", "(app-pages-browser)/./node_modules/next/link.js", "(app-pages-browser)/./node_modules/next/navigation.js", "(app-pages-browser)/./src/app/auth/page.tsx"]}