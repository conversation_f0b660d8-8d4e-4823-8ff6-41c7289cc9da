# CustomChess Board Editor Setup Guide

This guide explains how to set up and use the CustomChess board editor functionality.

## Features Implemented

### ✅ Completed Features

1. **Interactive 8x8 Chess Board**
   - Real-time board preview using react-chessboard
   - Click to place/remove pieces
   - FEN notation display
   - Piece count tracking

2. **Piece Customization**
   - Default piece sets (Classic, Modern, Wooden, Glass)
   - Custom piece upload with image validation
   - AI-powered piece generation using OpenAI DALL-E
   - Image moderation using OpenAI Moderation API

3. **Board Theme Customization**
   - 6 preset themes (Classic, Modern, Blue, Green, Purple, Dark)
   - Custom color picker for light/dark squares and border
   - Real-time theme preview

4. **Save Functionality**
   - Save board metadata to Supabase
   - Save custom piece images to Supabase Storage
   - User authentication required

5. **Image Safety**
   - File type validation (PNG, WebP, JPEG, GIF)
   - File size limits (5MB max)
   - Image dimension validation (min 64x64px)
   - OpenAI content moderation

## Setup Instructions

### 1. Environment Variables

Add the following environment variables to your `.env.local` file:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
```

### 2. Database Setup

Run the following SQL commands in your Supabase SQL editor:

```sql
-- Run the schema.sql file first
-- Then run the storage.sql file
```

### 3. Storage Bucket Setup

The storage bucket `chess-pieces` will be created automatically when you run the storage.sql file. Make sure your Supabase project has storage enabled.

### 4. Dependencies

All required dependencies are already included in `package.json`:

- `chess.js` - Chess game logic
- `react-chessboard` - Chess board component
- `@supabase/supabase-js` - Supabase client
- `lucide-react` - Icons

## Usage Guide

### Creating a Custom Board

1. **Navigate to Board Editor**
   - Go to `/board-editor` (requires authentication)
   - You'll be redirected to login if not authenticated

2. **Customize Board Theme**
   - Click the "Board" tab in the sidebar
   - Choose from preset themes or customize colors manually
   - Preview changes in real-time

3. **Add Custom Pieces**
   - Click the "Pieces" tab
   - Switch to "Custom Pieces" tab
   - For each piece type (King, Queen, Rook, etc.):
     - **Upload Image**: Click "Choose Image" and select a file
     - **AI Generation**: Enter a style prompt and click "Generate"

4. **Place Pieces on Board**
   - Select a piece from the piece selector
   - Click on any square to place it
   - Use the eraser tool to remove pieces
   - Use "Reset to Starting Position" for standard chess setup

5. **Save Your Board**
   - Enter a board name in the header
   - Click "Save Board"
   - Your board will be saved to the database

### File Requirements

**Uploaded Images:**
- Format: PNG, WebP, JPEG, or GIF
- Size: Maximum 5MB
- Dimensions: Minimum 64x64 pixels
- Background: Transparent preferred

**AI Generation:**
- Prompts should describe the style (e.g., "cyberpunk", "wooden texture", "glass effect")
- Generated images are 1024x1024 and can be resized

## API Endpoints

### `/api/generate-piece`
Generates AI chess pieces using OpenAI DALL-E.

**Request:**
```json
{
  "prompt": "cyberpunk style",
  "pieceType": "king",
  "pieceColor": "white"
}
```

**Response:**
```json
{
  "success": true,
  "urls": ["https://openai.com/generated-image-url"]
}
```

### `/api/moderate-image`
Moderates uploaded images using OpenAI Moderation API.

**Request:**
```json
{
  "image": "base64_encoded_image"
}
```

**Response:**
```json
{
  "success": true,
  "flagged": false,
  "categories": {}
}
```

## Database Schema

### Boards Table
```sql
CREATE TABLE public.boards (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    theme JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Pieces Table
```sql
CREATE TABLE public.pieces (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    board_id UUID REFERENCES public.boards(id) ON DELETE CASCADE NOT NULL,
    type piece_type NOT NULL,
    color piece_color NOT NULL,
    image_url TEXT NOT NULL
);
```

## Security Features

1. **Authentication Required**: All board operations require user authentication
2. **Row Level Security**: Database policies ensure users can only access their own data
3. **Image Moderation**: All uploaded images are checked for inappropriate content
4. **File Validation**: Strict file type and size validation
5. **Storage Security**: Supabase storage with proper RLS policies

## Troubleshooting

### Common Issues

1. **"OpenAI API not configured"**
   - Ensure `OPENAI_API_KEY` is set in your environment variables
   - Check that the API key is valid and has sufficient credits

2. **"Failed to upload image"**
   - Check file size (must be < 5MB)
   - Verify file format (PNG, WebP, JPEG, GIF)
   - Ensure Supabase storage is properly configured

3. **"Image content violates guidelines"**
   - The uploaded image was flagged by OpenAI's moderation
   - Try a different image or adjust the content

4. **"Failed to save board"**
   - Check database connection
   - Verify user authentication
   - Check Supabase service role key

### Development Notes

- The board editor uses chess.js for game logic and move validation
- Custom pieces are stored in Supabase Storage with public read access
- AI-generated images are temporarily stored at OpenAI URLs (consider downloading and storing them for production)
- All database operations are protected by Row Level Security policies

## Next Steps

To complete the CustomChess platform, you'll need to implement:

1. **Game Room Creation**: Allow users to create games with their custom boards
2. **Real-time Game Play**: Implement Supabase Realtime for live chess games
3. **Game Logic**: Add move validation, checkmate detection, etc.
4. **Timer System**: Implement chess clocks for different time controls
5. **Game History**: Store and display move history in PGN format

The board editor provides the foundation for custom chess sets that can be used in actual gameplay. 