# Supabase Setup Guide

## 1. Database Schema Setup

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Create a new query and copy the contents of `supabase/schema.sql`
4. Run the query to create all tables, types, and policies

## 2. Storage Setup

1. In the SQL Editor, create another new query
2. Copy the contents of `supabase/storage.sql`
3. Run the query to create storage buckets and policies

## 3. Seed Data (Optional)

1. In the SQL Editor, create another new query
2. Copy the contents of `supabase/seed.sql`
3. Run the query to create default board themes

## 4. Authentication Setup

1. Go to **Authentication** > **Settings** in your Supabase dashboard
2. Configure the following settings:

### Site URL
- Set to: `http://localhost:3000` (for development)
- For production, set to your actual domain

### Redirect URLs
Add these URLs:
- `http://localhost:3000/auth/callback` (for development)
- Your production domain + `/auth/callback` (for production)

### Email Templates (Optional)
You can customize the email templates for:
- Confirm signup
- Reset password
- Magic link

## 5. Storage Configuration

1. Go to **Storage** in your Supabase dashboard
2. You should see two buckets created:
   - `piece-images` - for storing custom chess piece images
   - `board-backgrounds` - for storing custom board background images

3. Both buckets are configured as public, meaning the images can be accessed directly via URL

## 6. Verify Setup

After running all the scripts, you should have:

### Tables:
- `users` - User profiles
- `boards` - Custom chess boards
- `pieces` - Chess piece configurations for each board
- `games` - Game sessions
- `board_themes` - Predefined board themes

### Storage Buckets:
- `piece-images` - For custom piece images
- `board-backgrounds` - For custom board backgrounds

### Row Level Security:
All tables have RLS enabled with appropriate policies to ensure users can only access their own data.

## 7. Test the Connection

You can test if everything is working by running the Next.js development server:

```bash
npm run dev
```

The app should start without any database connection errors.

## Troubleshooting

### Common Issues:

1. **Permission Denied Errors**: Make sure RLS policies are correctly set up
2. **Storage Access Issues**: Verify that storage buckets are created and policies are applied
3. **Authentication Issues**: Check that redirect URLs are correctly configured

### Checking Your Setup:

1. Go to **Table Editor** in Supabase to see all your tables
2. Go to **Storage** to verify buckets are created
3. Go to **Authentication** > **Users** to see user registrations (after testing signup)

## Next Steps

Once the database is set up, you can:
1. Test user registration and authentication
2. Create custom chess boards
3. Upload custom piece images
4. Start building the board editor interface
