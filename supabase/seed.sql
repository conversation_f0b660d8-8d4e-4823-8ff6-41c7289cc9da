-- Seed data for default chess pieces
-- This creates a default board with standard chess piece URLs

-- Insert a default board (you'll need to replace 'your-user-id' with an actual user ID)
-- This is just an example - in practice, boards will be created by users through the app

-- Example of how to create a default board with pieces
-- Note: This would typically be done through the application, not as seed data
-- But this shows the structure for reference

/*
-- Example board creation (uncomment and modify with real user ID when needed)
INSERT INTO public.boards (id, user_id, name, theme) VALUES 
(
    '00000000-0000-0000-0000-000000000001',
    'your-user-id-here', 
    'Classic Chess Set',
    '{"lightSquareColor": "#f0d9b5", "darkSquareColor": "#b58863", "mode": "light"}'
);

-- Example pieces for the default board
INSERT INTO public.pieces (board_id, type, color, image_url) VALUES 
-- White pieces
('00000000-0000-0000-0000-000000000001', 'king', 'white', '/default-pieces/white-king.svg'),
('00000000-0000-0000-0000-000000000001', 'queen', 'white', '/default-pieces/white-queen.svg'),
('00000000-0000-0000-0000-000000000001', 'rook', 'white', '/default-pieces/white-rook.svg'),
('00000000-0000-0000-0000-000000000001', 'knight', 'white', '/default-pieces/white-knight.svg'),
('00000000-0000-0000-0000-000000000001', 'bishop', 'white', '/default-pieces/white-bishop.svg'),
('00000000-0000-0000-0000-000000000001', 'pawn', 'white', '/default-pieces/white-pawn.svg'),

-- Black pieces
('00000000-0000-0000-0000-000000000001', 'king', 'black', '/default-pieces/black-king.svg'),
('00000000-0000-0000-0000-000000000001', 'queen', 'black', '/default-pieces/black-queen.svg'),
('00000000-0000-0000-0000-000000000001', 'rook', 'black', '/default-pieces/black-rook.svg'),
('00000000-0000-0000-0000-000000000001', 'knight', 'black', '/default-pieces/black-knight.svg'),
('00000000-0000-0000-0000-000000000001', 'bishop', 'black', '/default-pieces/black-bishop.svg'),
('00000000-0000-0000-0000-000000000001', 'pawn', 'black', '/default-pieces/black-pawn.svg');
*/

-- Create some sample board themes that users can choose from
-- These will be available as templates when creating new boards
CREATE TABLE IF NOT EXISTS public.board_themes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    theme JSONB NOT NULL,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default themes
INSERT INTO public.board_themes (name, theme, is_default) VALUES 
(
    'Classic Brown',
    '{"lightSquareColor": "#f0d9b5", "darkSquareColor": "#b58863", "mode": "light"}',
    true
),
(
    'Blue Ocean',
    '{"lightSquareColor": "#dee3e6", "darkSquareColor": "#8ca2ad", "mode": "light"}',
    false
),
(
    'Green Forest',
    '{"lightSquareColor": "#ffffdd", "darkSquareColor": "#86a666", "mode": "light"}',
    false
),
(
    'Purple Royal',
    '{"lightSquareColor": "#f3e5f5", "darkSquareColor": "#8e24aa", "mode": "light"}',
    false
),
(
    'Dark Mode',
    '{"lightSquareColor": "#3c3c3c", "darkSquareColor": "#1a1a1a", "mode": "dark"}',
    false
);

-- Enable RLS for board themes
ALTER TABLE public.board_themes ENABLE ROW LEVEL SECURITY;

-- Allow everyone to read board themes
CREATE POLICY "Anyone can view board themes" ON public.board_themes
    FOR SELECT USING (true);
