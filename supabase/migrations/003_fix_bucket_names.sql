-- This script fixes the storage bucket names from a previous faulty migration.
-- It is safe to run multiple times.

-- Step 1: Drop the policies from the incorrectly named buckets if they exist.
-- This is necessary before we can delete the buckets.
-- We ignore errors in case the policies or buckets don't exist.

DO $$
BEGIN
  DROP POLICY IF EXISTS "Users can upload piece images" ON storage.objects;
  DROP POLICY IF EXISTS "Anyone can view piece images" ON storage.objects;
  DROP POLICY IF EXISTS "Users can update their own piece images" ON storage.objects;
  DROP POLICY IF EXISTS "Users can delete their own piece images" ON storage.objects;
EXCEPTION WHEN aih_division_by_zero THEN
    -- Do nothing, policies did not exist
END $$;


-- Step 2: Delete the incorrectly named buckets if they exist.
DELETE FROM storage.buckets WHERE id = 'piece-images';
DELETE FROM storage.buckets WHERE id = 'board-backgrounds';


-- Step 3: Re-run the correct setup from the first migration.
-- This ensures the database is in the correct state.

INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('piece_images', 'piece_images', true),
    ('board_backgrounds', 'board_backgrounds', true)
ON CONFLICT (id) DO NOTHING;

-- Policies for piece_images bucket
DROP POLICY IF EXISTS "Users can upload piece images" ON storage.objects;
CREATE POLICY "Users can upload piece images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'piece_images' AND
        auth.role() = 'authenticated'
    );

DROP POLICY IF EXISTS "Anyone can view piece images" ON storage.objects;
CREATE POLICY "Anyone can view piece images" ON storage.objects
    FOR SELECT USING (bucket_id = 'piece_images');

DROP POLICY IF EXISTS "Users can update their own piece images" ON storage.objects;
CREATE POLICY "Users can update their own piece images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'piece_images' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    );

DROP POLICY IF EXISTS "Users can delete their own piece images" ON storage.objects;
CREATE POLICY "Users can delete their own piece images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'piece_images' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    );

-- Policies for board_backgrounds bucket
DROP POLICY IF EXISTS "Users can upload board backgrounds" ON storage.objects;
CREATE POLICY "Users can upload board backgrounds" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'board_backgrounds' AND
        auth.role() = 'authenticated'
    );

DROP POLICY IF EXISTS "Anyone can view board backgrounds" ON storage.objects;
CREATE POLICY "Anyone can view board backgrounds" ON storage.objects
    FOR SELECT USING (bucket_id = 'board_backgrounds');

DROP POLICY IF EXISTS "Users can update their own board backgrounds" ON storage.objects;
CREATE POLICY "Users can update their own board backgrounds" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'board_backgrounds' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    );

DROP POLICY IF EXISTS "Users can delete their own board backgrounds" ON storage.objects;
CREATE POLICY "Users can delete their own board backgrounds" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'board_backgrounds' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    ); 