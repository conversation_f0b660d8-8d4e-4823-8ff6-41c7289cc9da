-- Create a table to store user-uploaded and AI-generated custom images.
CREATE TABLE IF NOT EXISTS public.custom_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    name TEXT, -- Optional user-defined name for the image
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.custom_images ENABLE ROW LEVEL SECURITY;

-- Create policies for custom_images
-- Users can see their own images.
CREATE POLICY "Users can view their own custom images"
ON public.custom_images FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can insert new images for themselves.
CREATE POLICY "Users can insert their own custom images"
ON public.custom_images FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own images (e.g., to rename them).
CREATE POLICY "Users can update their own custom images"
ON public.custom_images FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- Users can delete their own images.
CREATE POLICY "Users can delete their own custom images"
ON public.custom_images FOR DELETE
TO authenticated
USING (auth.uid() = user_id); 