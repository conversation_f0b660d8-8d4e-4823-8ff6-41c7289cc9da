-- Create a migrations directory for organization
-- This is just a comment, no action needed.

-- Create idempotent storage setup script
-- This script can be run multiple times without causing errors.

-- Create storage buckets if they don't already exist
INSERT INTO storage.buckets (id, name, public) 
VALUES 
    ('piece_images', 'piece_images', true),
    ('board_backgrounds', 'board_backgrounds', true)
ON CONFLICT (id) DO NOTHING;

-- Revoke existing policies before creating new ones to avoid conflicts
-- This ensures the policies are always up-to-date with the script.

-- Policies for piece-images bucket
DROP POLICY IF EXISTS "Users can upload piece images" ON storage.objects;
CREATE POLICY "Users can upload piece images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'piece_images' AND
        auth.role() = 'authenticated'
    );

DROP POLICY IF EXISTS "Anyone can view piece images" ON storage.objects;
CREATE POLICY "Anyone can view piece images" ON storage.objects
    FOR SELECT USING (bucket_id = 'piece_images');

DROP POLICY IF EXISTS "Users can update their own piece images" ON storage.objects;
CREATE POLICY "Users can update their own piece images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'piece_images' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    );

DROP POLICY IF EXISTS "Users can delete their own piece images" ON storage.objects;
CREATE POLICY "Users can delete their own piece images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'piece_images' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    );

-- Policies for board-backgrounds bucket
DROP POLICY IF EXISTS "Users can upload board backgrounds" ON storage.objects;
CREATE POLICY "Users can upload board backgrounds" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'board_backgrounds' AND
        auth.role() = 'authenticated'
    );

DROP POLICY IF EXISTS "Anyone can view board backgrounds" ON storage.objects;
CREATE POLICY "Anyone can view board backgrounds" ON storage.objects
    FOR SELECT USING (bucket_id = 'board_backgrounds');

DROP POLICY IF EXISTS "Users can update their own board backgrounds" ON storage.objects;
CREATE POLICY "Users can update their own board backgrounds" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'board_backgrounds' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    );

DROP POLICY IF EXISTS "Users can delete their own board backgrounds" ON storage.objects;
CREATE POLICY "Users can delete their own board backgrounds" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'board_backgrounds' AND
        auth.uid() = (storage.foldername(name))[1]::uuid
    ); 